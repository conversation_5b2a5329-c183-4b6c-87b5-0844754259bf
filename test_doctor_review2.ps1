# 测试医生审核功能
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $token"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 测试获取待审核医生列表
    Write-Host "`n=== 测试获取待审核医生列表 ==="
    try {
        $pendingDoctorsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors?status=PENDING&page=1&size=10" -Method Get -Headers $headers
        Write-Host "待审核医生数量: $($pendingDoctorsResponse.data.totalElements)"
        
        if ($pendingDoctorsResponse.code -eq 200 -and $pendingDoctorsResponse.data.content.Count -gt 0) {
            $pendingDoctor = $pendingDoctorsResponse.data.content[0]
            $pendingDoctorUserId = $pendingDoctor.userId
            Write-Host "选择待审核医生: $($pendingDoctor.realName) (ID: $pendingDoctorUserId)"
            
            # 测试审核通过
            Write-Host "`n=== 测试审核通过 ==="
            try {
                $approveData = @{
                    status = "APPROVED"
                    reason = "资质审核通过，符合要求"
                }
                $approveBody = $approveData | ConvertTo-Json
                Write-Host "审核请求体: $approveBody"
                
                $approveResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors/$pendingDoctorUserId/review" -Method Put -Headers $headers -Body $approveBody
                Write-Host "审核通过响应: $($approveResponse | ConvertTo-Json)"
                
                # 再次查看该医生状态
                Write-Host "`n=== 验证审核结果 ==="
                $reviewDetailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors/$pendingDoctorUserId/review" -Method Get -Headers $headers
                Write-Host "审核后状态: $($reviewDetailResponse.data.statusDescription)"
                Write-Host "审核理由: $($reviewDetailResponse.data.reviewReason)"
                
            } catch {
                Write-Host "审核通过失败: $($_.Exception.Message)"
            }
        }
    } catch {
        Write-Host "获取待审核医生列表失败: $($_.Exception.Message)"
    }
} else {
    Write-Host "登录失败: $($loginResponse.message)"
}
