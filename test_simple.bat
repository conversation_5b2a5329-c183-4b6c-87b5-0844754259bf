@echo off
echo === 社区健康管理系统功能测试 ===

echo 1. 管理员登录测试...
curl -X POST "http://localhost:8080/api/user/login" -H "Content-Type: application/json" -d "{\"phoneNumber\":\"19999999999\",\"password\":\"admin888\"}"

echo.
echo 2. 医生登录测试...
curl -X POST "http://localhost:8080/api/user/login" -H "Content-Type: application/json" -d "{\"phoneNumber\":\"18610001001\",\"password\":\"123456\"}"

echo.
echo 3. 用户登录测试...
curl -X POST "http://localhost:8080/api/user/login" -H "Content-Type: application/json" -d "{\"phoneNumber\":\"13800000001\",\"password\":\"123456\"}"

echo.
echo === 测试完成 ===
echo 管理端: http://localhost:5174/admin (19999999999/admin888)
echo 医生端: http://localhost:5174/doctor (18610001001/123456)
echo 用户端: http://localhost:5174/ (13800000001/123456)
pause
