# 测试内容管理功能
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $token"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 测试获取所有内容列表
    Write-Host "`n=== 测试获取所有内容列表 ==="
    try {
        $allContentsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents?type=all&page=1&size=10" -Method Get -Headers $headers
        Write-Host "所有内容数量: $($allContentsResponse.data.totalElements)"
        Write-Host "内容列表: $($allContentsResponse.data.content.Count) 条"
    } catch {
        Write-Host "获取所有内容失败: $($_.Exception.Message)"
    }
    
    # 测试获取健康资讯列表
    Write-Host "`n=== 测试获取健康资讯列表 ==="
    try {
        $newsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents?type=news&page=1&size=10" -Method Get -Headers $headers
        Write-Host "健康资讯数量: $($newsResponse.data.totalElements)"
    } catch {
        Write-Host "获取健康资讯失败: $($_.Exception.Message)"
    }
    
    # 测试获取医生指导列表
    Write-Host "`n=== 测试获取医生指导列表 ==="
    try {
        $guidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents?type=guidance&page=1&size=10" -Method Get -Headers $headers
        Write-Host "医生指导数量: $($guidanceResponse.data.totalElements)"
    } catch {
        Write-Host "获取医生指导失败: $($_.Exception.Message)"
    }
    
    # 测试创建新内容
    Write-Host "`n=== 测试创建新内容 ==="
    try {
        $createData = @{
            contentType = "NEWS"
            title = "测试健康资讯标题"
            body = "这是一条测试健康资讯的内容，包含了健康相关的信息和建议。"
        }
        $createBody = $createData | ConvertTo-Json
        Write-Host "创建请求体: $createBody"
        
        $createResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Post -Headers $headers -Body $createBody
        Write-Host "创建内容响应: $($createResponse | ConvertTo-Json)"
        
        if ($createResponse.code -eq 200) {
            $newContentId = $createResponse.data.id
            Write-Host "新内容ID: $newContentId"
            
            # 测试获取内容详情
            Write-Host "`n=== 测试获取内容详情 ==="
            $detailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents/$newContentId" -Method Get -Headers $headers
            Write-Host "内容详情: $($detailResponse.data.title)"
            
            # 测试更新内容
            Write-Host "`n=== 测试更新内容 ==="
            $updateData = @{
                title = "更新后的健康资讯标题"
                body = "这是更新后的健康资讯内容，包含了更多详细的健康信息。"
            }
            $updateBody = $updateData | ConvertTo-Json
            $updateResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents/$newContentId" -Method Put -Headers $headers -Body $updateBody
            Write-Host "更新内容响应: $($updateResponse | ConvertTo-Json)"
            
            # 测试删除内容
            Write-Host "`n=== 测试删除内容 ==="
            $deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents/$newContentId" -Method Delete -Headers $headers
            Write-Host "删除内容响应: $($deleteResponse | ConvertTo-Json)"
        }
    } catch {
        Write-Host "创建内容失败: $($_.Exception.Message)"
    }
    
} else {
    Write-Host "登录失败: $($loginResponse.message)"
}
