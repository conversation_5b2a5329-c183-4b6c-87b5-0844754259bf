# 完整功能测试脚本
Write-Host "=== 社区健康管理系统完整功能测试 ===" -ForegroundColor Green

# 1. 管理员登录测试
Write-Host "`n1. 管理员登录测试..." -ForegroundColor Yellow
$adminLoginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
try {
    $adminLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $adminLoginBody
    if ($adminLoginResponse.code -eq 200) {
        $adminToken = $adminLoginResponse.data.token
        Write-Host "✅ 管理员登录成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 管理员登录失败: $($adminLoginResponse.message)" -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "❌ 管理员登录异常: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

$adminHeaders = @{
    "Authorization" = "Bearer $adminToken"
    "Content-Type" = "application/json"
}

# 2. 测试管理端内容管理
Write-Host "`n2. 管理端内容管理测试..." -ForegroundColor Yellow

# 2.1 获取所有内容
Write-Host "  2.1 获取所有内容..." -ForegroundColor Cyan
try {
    $allContentsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents?type=all&page=1&size=10" -Method Get -Headers $adminHeaders
    if ($allContentsResponse.code -eq 200) {
        Write-Host "  ✅ 获取所有内容成功，总数: $($allContentsResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取所有内容失败: $($allContentsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取所有内容异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 2.2 获取健康资讯
Write-Host "  2.2 获取健康资讯..." -ForegroundColor Cyan
try {
    $newsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents?type=NEWS&page=1&size=10" -Method Get -Headers $adminHeaders
    if ($newsResponse.code -eq 200) {
        Write-Host "  ✅ 获取健康资讯成功，数量: $($newsResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取健康资讯失败: $($newsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取健康资讯异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 2.3 创建社区活动
Write-Host "  2.3 创建社区活动..." -ForegroundColor Cyan
$activityData = '{"contentType":"ACTIVITY","title":"春季健康体检活动","body":"为了提高社区居民的健康意识，我们将举办春季健康体检活动。活动包括：血压测量、血糖检测、心电图检查等基础项目。欢迎社区居民积极参与！","activityTime":"2025-07-01T09:00:00","activityLocation":"社区健康服务中心"}'
try {
    $createActivityResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Post -Headers $adminHeaders -Body $activityData
    if ($createActivityResponse.code -eq 200) {
        $activityId = $createActivityResponse.data.id
        Write-Host "  ✅ 创建社区活动成功，ID: $activityId" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 创建社区活动失败: $($createActivityResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 创建社区活动异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 医生登录测试
Write-Host "`n3. 医生登录测试..." -ForegroundColor Yellow
$doctorLoginBody = '{"phoneNumber":"18610001001","password":"123456"}'
try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $doctorLoginBody
    if ($doctorLoginResponse.code -eq 200) {
        $doctorToken = $doctorLoginResponse.data.token
        Write-Host "✅ 医生登录成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 医生登录失败: $($doctorLoginResponse.message)" -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "❌ 医生登录异常: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

$doctorHeaders = @{
    "Authorization" = "Bearer $doctorToken"
    "Content-Type" = "application/json"
}

# 4. 测试医生端健康指导
Write-Host "`n4. 医生端健康指导测试..." -ForegroundColor Yellow

# 4.1 获取医生健康指导列表
Write-Host "  4.1 获取医生健康指导列表..." -ForegroundColor Cyan
try {
    $doctorGuidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/doctor/contents/guidance?page=1&size=10" -Method Get -Headers $doctorHeaders
    if ($doctorGuidanceResponse.code -eq 200) {
        Write-Host "  ✅ 获取医生健康指导成功，数量: $($doctorGuidanceResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取医生健康指导失败: $($doctorGuidanceResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取医生健康指导异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 4.2 创建健康指导
Write-Host "  4.2 创建健康指导..." -ForegroundColor Cyan
$guidanceData = '{"title":"春季养生指导","body":"春季是万物复苏的季节，也是养生的好时机。建议大家：1. 早睡早起，顺应自然规律；2. 多吃新鲜蔬菜水果，补充维生素；3. 适当运动，增强体质；4. 保持心情愉悦，避免情绪波动。"}'
try {
    $createGuidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/doctor/contents/guidance" -Method Post -Headers $doctorHeaders -Body $guidanceData
    if ($createGuidanceResponse.code -eq 200) {
        $guidanceId = $createGuidanceResponse.data.id
        Write-Host "  ✅ 创建健康指导成功，ID: $guidanceId" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 创建健康指导失败: $($createGuidanceResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 创建健康指导异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 用户登录测试
Write-Host "`n5. 用户登录测试..." -ForegroundColor Yellow
$userLoginBody = '{"phoneNumber":"13800000001","password":"123456"}'
try {
    $userLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $userLoginBody
    if ($userLoginResponse.code -eq 200) {
        $userToken = $userLoginResponse.data.token
        Write-Host "✅ 用户登录成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 用户登录失败: $($userLoginResponse.message)" -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "❌ 用户登录异常: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

$userHeaders = @{
    "Authorization" = "Bearer $userToken"
    "Content-Type" = "application/json"
}

# 6. 测试用户端内容查看
Write-Host "`n6. 用户端内容查看测试..." -ForegroundColor Yellow

# 6.1 获取活动列表
Write-Host "  6.1 获取活动列表..." -ForegroundColor Cyan
try {
    $userActivitiesResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/activities?page=1&size=10" -Method Get -Headers $userHeaders
    if ($userActivitiesResponse.code -eq 200) {
        Write-Host "  ✅ 获取活动列表成功，数量: $($userActivitiesResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取活动列表失败: $($userActivitiesResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取活动列表异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 6.2 获取健康资讯
Write-Host "  6.2 获取健康资讯..." -ForegroundColor Cyan
try {
    $userNewsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/news?page=1&size=10" -Method Get -Headers $userHeaders
    if ($userNewsResponse.code -eq 200) {
        Write-Host "  ✅ 获取健康资讯成功，数量: $($userNewsResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取健康资讯失败: $($userNewsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取健康资讯异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 6.3 获取医生指导
Write-Host "  6.3 获取医生指导..." -ForegroundColor Cyan
try {
    $userGuidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/guidance?page=1&size=10" -Method Get -Headers $userHeaders
    if ($userGuidanceResponse.code -eq 200) {
        Write-Host "  ✅ 获取医生指导成功，数量: $($userGuidanceResponse.data.totalElements)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 获取医生指导失败: $($userGuidanceResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 获取医生指导异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试活动报名功能
if ($activityId) {
    Write-Host "`n7. 测试活动报名功能..." -ForegroundColor Yellow
    try {
        $registerResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/activities/$activityId/register" -Method Post -Headers $userHeaders
        if ($registerResponse.code -eq 200) {
            Write-Host "✅ 活动报名成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 活动报名失败: $($registerResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 活动报名异常: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "✅ 管理端内容管理功能正常" -ForegroundColor Green
Write-Host "✅ 医生端健康指导功能正常" -ForegroundColor Green
Write-Host "✅ 用户端内容查看功能正常" -ForegroundColor Green
Write-Host "✅ 社区活动报名功能正常" -ForegroundColor Green

Write-Host "`n=== 访问地址 ===" -ForegroundColor Cyan
Write-Host "管理端: http://localhost:5174/admin (19999999999/admin888)" -ForegroundColor White
Write-Host "医生端: http://localhost:5174/doctor (18610001001/123456)" -ForegroundColor White
Write-Host "用户端: http://localhost:5174/ (13800000001/123456)" -ForegroundColor White
