import api from './index'

/**
 * 管理员相关API
 */

/**
 * 获取管理员统计信息
 * @returns {Promise} 统计信息响应
 */
export const getDashboardStatistics = () => {
  return api.get('/admin/statistics')
}

/**
 * 获取用户列表（居民用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise} 用户列表响应
 */
export const getUsers = (params) => {
  return api.get('/admin/users', { params })
}

/**
 * 获取用户详细信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户详情响应
 */
export const getUserDetail = (userId) => {
  return api.get(`/admin/users/${userId}`)
}

/**
 * 更新用户状态
 * @param {number} userId - 用户ID
 * @param {string} status - 用户状态 (ACTIVE/DISABLED)
 * @returns {Promise} 更新响应
 */
export const updateUserStatus = (userId, status) => {
  return api.put(`/admin/users/${userId}/status`, { status })
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise} 删除响应
 */
export const deleteUser = (userId) => {
  return api.delete(`/admin/users/${userId}`)
}

/**
 * 重置用户密码
 * @param {number} userId - 用户ID
 * @returns {Promise} 重置密码响应
 */
export const resetUserPassword = (userId) => {
  return api.post(`/admin/users/${userId}/reset-password`)
}

/**
 * 获取医生列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @param {string} params.status - 医生状态
 * @returns {Promise} 医生列表响应
 */
export const getDoctors = (params) => {
  return api.get('/admin/doctors', { params })
}

/**
 * 创建医生记录
 * @param {Object} doctorData - 医生数据
 * @returns {Promise} 创建响应
 */
export const createDoctor = (doctorData) => {
  return api.post('/admin/doctors', doctorData)
}

/**
 * 更新医生状态
 * @param {number} doctorUserId - 医生用户ID
 * @param {string} status - 医生状态
 * @returns {Promise} 更新响应
 */
export const updateDoctorStatus = (doctorUserId, status) => {
  return api.put(`/admin/doctors/${doctorUserId}/status`, { status })
}

/**
 * 获取健康数据统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 健康统计响应
 */
export const getHealthStatistics = (params) => {
  return api.get('/admin/statistics/health', { params })
}

/**
 * 获取预约统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 预约统计响应
 */
export const getAppointmentStatistics = (params) => {
  return api.get('/admin/statistics/appointments', { params })
}
