<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <aside class="admin-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">🛡️</div>
          <h2>管理控制台</h2>
        </div>
        <div class="admin-badge">
          <span class="badge-text">ADMIN</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-item active" @click="setActiveTab('overview')">
          <div class="nav-icon">📊</div>
          <span>系统概览</span>
        </div>
        <div class="nav-item" @click="setActiveTab('users')">
          <div class="nav-icon">👥</div>
          <span>用户管理</span>
        </div>
        <div class="nav-item" @click="setActiveTab('doctors')">
          <div class="nav-icon">⚕️</div>
          <span>医生管理</span>
          <div class="pending-badge" v-if="pendingDoctors > 0">{{ pendingDoctors }}</div>
        </div>
        <div class="nav-item" @click="setActiveTab('content')">
          <div class="nav-icon">📝</div>
          <span>内容管理</span>
        </div>
        <div class="nav-item" @click="setActiveTab('announcements')">
          <div class="nav-icon">📢</div>
          <span>系统公告</span>
        </div>
        <div class="nav-item" @click="setActiveTab('settings')">
          <div class="nav-icon">⚙️</div>
          <span>系统设置</span>
        </div>
      </nav>
      
      <div class="sidebar-footer">
        <div class="admin-info">
          <div class="admin-avatar">{{ userInfo?.nickname?.charAt(0) || 'A' }}</div>
          <div class="admin-details">
            <div class="admin-name">{{ userInfo?.nickname || '管理员' }}</div>
            <div class="admin-role">系统管理员</div>
            <div class="admin-status">在线</div>
          </div>
        </div>
        <button @click="handleLogout" class="logout-btn">
          <div class="logout-icon">🚪</div>
          退出系统
        </button>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main class="admin-main">
      <!-- 系统概览 -->
      <div v-if="activeTab === 'overview'" class="overview-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>系统概览</h1>
          <div class="header-actions">
            <div class="last-update">最后更新: {{ getCurrentTime() }}</div>
            <button class="refresh-btn" @click="refreshData">
              <span class="refresh-icon">🔄</span>
              刷新数据
            </button>
          </div>
        </div>

        <!-- 核心数据指标卡片 -->
        <div class="metrics-grid">
          <div class="metric-card primary">
            <div class="metric-header">
              <div class="metric-icon">👥</div>
              <div class="metric-trend up">↗ +12%</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ totalUsers.toLocaleString() }}</div>
              <div class="metric-label">总注册用户</div>
              <div class="metric-detail">本月新增 {{ newUsersThisMonth }} 人</div>
            </div>
          </div>

          <div class="metric-card success">
            <div class="metric-header">
              <div class="metric-icon">⚕️</div>
              <div class="metric-trend up">↗ +3</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ verifiedDoctors }}</div>
              <div class="metric-label">认证医生数</div>
              <div class="metric-detail">待审核 {{ pendingDoctors }} 人</div>
            </div>
          </div>

          <div class="metric-card warning">
            <div class="metric-header">
              <div class="metric-icon">🔥</div>
              <div class="metric-trend stable">→ 稳定</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ activeUsersToday }}</div>
              <div class="metric-label">今日活跃用户</div>
              <div class="metric-detail">在线用户 {{ onlineUsers }} 人</div>
            </div>
          </div>

          <div class="metric-card info">
            <div class="metric-header">
              <div class="metric-icon">📅</div>
              <div class="metric-trend up">↗ +8%</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ totalAppointments.toLocaleString() }}</div>
              <div class="metric-label">平台总预约量</div>
              <div class="metric-detail">今日预约 {{ todayAppointments }} 次</div>
            </div>
          </div>
        </div>

        <!-- 系统状态和待办事项 -->
        <div class="dashboard-grid">
          <!-- 系统健康状态 -->
          <div class="system-health-card">
            <div class="card-header">
              <h3>🖥️ 系统健康状态</h3>
              <div class="health-status" :class="systemHealth.status">
                {{ systemHealth.text }}
              </div>
            </div>
            <div class="card-content">
              <div class="health-metrics">
                <div class="health-item">
                  <div class="health-label">服务器负载</div>
                  <div class="health-value">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: systemHealth.serverLoad + '%' }"></div>
                    </div>
                    <span>{{ systemHealth.serverLoad }}%</span>
                  </div>
                </div>
                <div class="health-item">
                  <div class="health-label">数据库连接</div>
                  <div class="health-value">
                    <span class="status-dot" :class="systemHealth.dbStatus"></span>
                    {{ systemHealth.dbStatus === 'healthy' ? '正常' : '异常' }}
                  </div>
                </div>
                <div class="health-item">
                  <div class="health-label">API响应时间</div>
                  <div class="health-value">{{ systemHealth.apiResponseTime }}ms</div>
                </div>
                <div class="health-item">
                  <div class="health-label">存储空间</div>
                  <div class="health-value">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: systemHealth.storageUsage + '%' }"></div>
                    </div>
                    <span>{{ systemHealth.storageUsage }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 待办事项表格 -->
          <div class="pending-tasks-card">
            <div class="card-header">
              <h3>⚡ 待办事项</h3>
              <div class="task-summary">{{ pendingTasks.length }} 项待处理</div>
            </div>
            <div class="card-content">
              <div class="tasks-table">
                <div class="table-header">
                  <div class="col-type">类型</div>
                  <div class="col-content">内容</div>
                  <div class="col-time">提交时间</div>
                  <div class="col-priority">优先级</div>
                  <div class="col-actions">操作</div>
                </div>
                <div v-for="task in pendingTasks" :key="task.id" class="table-row" :class="task.priority">
                  <div class="col-type">
                    <span class="task-type-badge" :class="task.type">{{ getTaskTypeText(task.type) }}</span>
                  </div>
                  <div class="col-content">
                    <div class="task-title">{{ task.title }}</div>
                    <div class="task-subtitle">{{ task.subtitle }}</div>
                  </div>
                  <div class="col-time">{{ task.submitTime }}</div>
                  <div class="col-priority">
                    <span class="priority-badge" :class="task.priority">{{ getPriorityText(task.priority) }}</span>
                  </div>
                  <div class="col-actions">
                    <button class="action-btn approve" @click="approveTask(task)" v-if="task.type === 'doctor-review'">
                      批准
                    </button>
                    <button class="action-btn reject" @click="rejectTask(task)" v-if="task.type === 'doctor-review'">
                      拒绝
                    </button>
                    <button class="action-btn edit" @click="editTask(task)" v-if="task.type === 'content-review'">
                      编辑发布
                    </button>
                    <button class="action-btn view" @click="viewTask(task)">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据图表区域 -->
        <div class="charts-section">
          <div class="chart-card">
            <div class="chart-header">
              <h3>📈 用户增长趋势</h3>
              <div class="chart-controls">
                <select class="time-selector">
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                </select>
              </div>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                📊 用户增长趋势图表
                <div class="chart-note">集成 ECharts 后显示真实数据</div>
              </div>
            </div>
          </div>

          <div class="chart-card">
            <div class="chart-header">
              <h3>🏥 预约统计分析</h3>
              <div class="chart-controls">
                <select class="dept-selector">
                  <option value="all">全部科室</option>
                  <option value="internal">内科</option>
                  <option value="surgery">外科</option>
                  <option value="pediatrics">儿科</option>
                </select>
              </div>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                📊 预约量统计图表
                <div class="chart-note">按科室分类的预约数据分析</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户管理 -->
      <div v-if="activeTab === 'users'" class="users-content">
        <div class="page-header">
          <h1>用户管理</h1>
          <div class="header-actions">
            <div class="search-box">
              <input
                type="text"
                placeholder="搜索用户手机号或昵称..."
                v-model="userSearchKeyword"
                @keyup.enter="searchUsers"
              />
              <button @click="searchUsers">🔍</button>
            </div>
          </div>
        </div>

        <div class="data-table">
          <table>
            <thead>
              <tr>
                <th>用户ID</th>
                <th>手机号</th>
                <th>昵称</th>
                <th>注册时间</th>
                <th>账户状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in usersList" :key="user.id">
                <td>{{ user.id }}</td>
                <td>{{ user.phoneNumber }}</td>
                <td>{{ user.nickname || '未设置' }}</td>
                <td>{{ formatDateTime(user.createdAt) }}</td>
                <td>
                  <span class="status-badge" :class="user.statusCode === 'active' ? 'active' : 'disabled'">
                    {{ user.status }}
                  </span>
                </td>
                <td>
                  <button class="btn-small btn-info" @click="viewUserDetail(user)">详情</button>
                  <button
                    class="btn-small"
                    :class="user.statusCode === 'active' ? 'btn-warning' : 'btn-success'"
                    @click="toggleUserStatus(user)">
                    {{ user.statusCode === 'active' ? '禁用' : '启用' }}
                  </button>
                  <button class="btn-small btn-warning" @click="resetPassword(user.id)">重置密码</button>
                  <button class="btn-small btn-danger" @click="deleteUser(user.id)">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="pagination">
          <button @click="prevUsersPage" :disabled="usersPage <= 1">上一页</button>
          <span>第 {{ usersPage }} 页</span>
          <button @click="nextUsersPage">下一页</button>
        </div>
      </div>

      <!-- 医生管理 -->
      <div v-if="activeTab === 'doctors'" class="doctors-content">
        <div class="page-header">
          <h1>医生管理</h1>
          <div class="header-actions">
            <div class="filter-tabs">
              <button
                class="filter-tab"
                :class="{ active: doctorFilter === 'all' }"
                @click="setDoctorFilter('all')"
              >
                全部医生
              </button>
              <button
                class="filter-tab"
                :class="{ active: doctorFilter === 'PENDING' }"
                @click="setDoctorFilter('PENDING')"
              >
                待审核 <span class="badge" v-if="pendingDoctors > 0">{{ pendingDoctors }}</span>
              </button>
              <button
                class="filter-tab"
                :class="{ active: doctorFilter === 'APPROVED' }"
                @click="setDoctorFilter('APPROVED')"
              >
                已通过
              </button>
            </div>
          </div>
        </div>

        <div class="data-table">
          <table>
            <thead>
              <tr>
                <th>医生ID</th>
                <th>姓名</th>
                <th>科室</th>
                <th>职称</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="doctor in doctorsList" :key="doctor.userId">
                <td>{{ doctor.userId }}</td>
                <td>{{ doctor.realName }}</td>
                <td>{{ doctor.departmentName || '未知科室' }}</td>
                <td>{{ doctor.title || '未设置' }}</td>
                <td>
                  <span class="status-badge" :class="doctor.status.toLowerCase()">
                    {{ getStatusText(doctor.status) }}
                  </span>
                </td>
                <td>
                  <button
                    v-if="doctor.status === 'PENDING'"
                    class="btn-small btn-info"
                    @click="reviewDoctor(doctor)">
                    审核
                  </button>
                  <button
                    v-else
                    class="btn-small btn-info"
                    @click="viewDoctorDetail(doctor)">
                    查看详情
                  </button>
                  <button
                    v-if="doctor.status === 'APPROVED'"
                    class="btn-small btn-warning"
                    @click="editDoctorInfo(doctor)">
                    编辑信息
                  </button>
                  <button
                    v-if="doctor.status === 'APPROVED'"
                    class="btn-small btn-danger"
                    @click="disableDoctor(doctor.userId)">
                    禁用
                  </button>
                  <button
                    v-if="doctor.status === 'DISABLED'"
                    class="btn-small btn-success"
                    @click="enableDoctor(doctor.userId)">
                    启用
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="pagination">
          <button @click="prevDoctorsPage" :disabled="doctorsPage <= 1">上一页</button>
          <span>第 {{ doctorsPage }} 页</span>
          <button @click="nextDoctorsPage">下一页</button>
        </div>
      </div>

      <!-- 其他页面内容占位 -->
      <div v-else-if="!['overview', 'users', 'doctors'].includes(activeTab)" class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">🚧</div>
          <h2>{{ getPageTitle() }}</h2>
          <p>此功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </main>

    <!-- 用户详情弹窗 -->
    <div v-if="showUserDetailModal" class="modal-overlay" @click="closeUserDetailModal">
      <div class="modal-content user-detail-modal" @click.stop>
        <div class="modal-header">
          <h3>用户详细信息</h3>
          <button class="close-btn" @click="closeUserDetailModal">&times;</button>
        </div>

        <div class="modal-body" v-if="selectedUserDetail">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>用户ID:</label>
                <span>{{ selectedUserDetail.id }}</span>
              </div>
              <div class="detail-item">
                <label>手机号:</label>
                <span>{{ selectedUserDetail.phoneNumber }}</span>
              </div>
              <div class="detail-item">
                <label>昵称:</label>
                <span>{{ selectedUserDetail.nickname || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>角色:</label>
                <span>{{ selectedUserDetail.roleDescription }}</span>
              </div>
              <div class="detail-item">
                <label>状态:</label>
                <span class="status-badge" :class="selectedUserDetail.status === 'ACTIVE' ? 'active' : 'disabled'">
                  {{ selectedUserDetail.statusDescription }}
                </span>
              </div>
              <div class="detail-item">
                <label>注册时间:</label>
                <span>{{ formatDateTime(selectedUserDetail.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="detail-section">
            <h4>统计信息</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ selectedUserDetail.healthProfileCount || 0 }}</div>
                <div class="stat-label">健康档案</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ selectedUserDetail.appointmentCount || 0 }}</div>
                <div class="stat-label">预约次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ selectedUserDetail.consultationCount || 0 }}</div>
                <div class="stat-label">咨询次数</div>
              </div>
            </div>
          </div>

          <!-- 健康档案列表 -->
          <div class="detail-section" v-if="selectedUserDetail.healthProfiles && selectedUserDetail.healthProfiles.length > 0">
            <h4>健康档案</h4>
            <div class="profiles-list">
              <div v-for="profile in selectedUserDetail.healthProfiles" :key="profile.id" class="profile-item">
                <div class="profile-header">
                  <span class="profile-name">{{ profile.profileOwnerName }}</span>
                  <span class="profile-info">{{ profile.gender }} {{ profile.age ? profile.age + '岁' : '' }}</span>
                </div>
                <div class="profile-history" v-if="profile.medicalHistory">
                  <strong>病史:</strong> {{ profile.medicalHistory }}
                </div>
              </div>
            </div>
          </div>

          <div class="detail-section" v-else>
            <h4>健康档案</h4>
            <p class="no-data">该用户暂无健康档案</p>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeUserDetailModal">关闭</button>
        </div>
      </div>
    </div>

    <!-- 医生审核弹窗 -->
    <div v-if="showDoctorReviewModal" class="modal-overlay" @click="closeDoctorReviewModal">
      <div class="modal-content doctor-review-modal" @click.stop>
        <div class="modal-header">
          <h3>医生资质审核</h3>
          <button class="close-btn" @click="closeDoctorReviewModal">&times;</button>
        </div>

        <div class="modal-body" v-if="selectedDoctorReview">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>申请人昵称:</label>
                <span>{{ selectedDoctorReview.nickname || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>手机号:</label>
                <span>{{ selectedDoctorReview.phoneNumber }}</span>
              </div>
              <div class="detail-item">
                <label>真实姓名:</label>
                <span>{{ selectedDoctorReview.realName }}</span>
              </div>
              <div class="detail-item">
                <label>性别:</label>
                <span>{{ selectedDoctorReview.gender || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>年龄:</label>
                <span>{{ selectedDoctorReview.age || '未设置' }}岁</span>
              </div>
              <div class="detail-item">
                <label>身份证号:</label>
                <span>{{ selectedDoctorReview.idCardNumber || '未设置' }}</span>
              </div>
            </div>
          </div>

          <!-- 专业信息 -->
          <div class="detail-section">
            <h4>专业信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>申请科室:</label>
                <span>{{ selectedDoctorReview.departmentName }}</span>
              </div>
              <div class="detail-item">
                <label>职称:</label>
                <span>{{ selectedDoctorReview.title || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>专长:</label>
                <span>{{ selectedDoctorReview.specialty || '未设置' }}</span>
              </div>
              <div class="detail-item">
                <label>执业证书编号:</label>
                <span>{{ selectedDoctorReview.licenseNumber || '未设置' }}</span>
              </div>
            </div>
            <div class="detail-item full-width" v-if="selectedDoctorReview.bio">
              <label>个人简介:</label>
              <p class="bio-text">{{ selectedDoctorReview.bio }}</p>
            </div>
          </div>

          <!-- 证件照片 -->
          <div class="detail-section">
            <h4>证件照片</h4>
            <div class="photos-grid">
              <div class="photo-item" v-if="selectedDoctorReview.idCardPhotoUrl">
                <label>身份证照片:</label>
                <img :src="selectedDoctorReview.idCardPhotoUrl" alt="身份证照片" class="cert-photo">
              </div>
              <div class="photo-item" v-if="selectedDoctorReview.qualificationPhotoUrl">
                <label>资质证书:</label>
                <img :src="selectedDoctorReview.qualificationPhotoUrl" alt="资质证书" class="cert-photo">
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="detail-section">
            <h4>统计信息</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ selectedDoctorReview.appointmentCount || 0 }}</div>
                <div class="stat-label">预约次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ selectedDoctorReview.consultationCount || 0 }}</div>
                <div class="stat-label">咨询次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ selectedDoctorReview.prescriptionCount || 0 }}</div>
                <div class="stat-label">处方数量</div>
              </div>
            </div>
          </div>

          <!-- 审核理由输入 -->
          <div class="detail-section">
            <h4>审核意见</h4>
            <textarea
              v-model="reviewReason"
              placeholder="请输入审核理由（可选）"
              class="review-reason-input"
              rows="3">
            </textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-success" @click="approveDoctorReview" :disabled="reviewSubmitting">
            {{ reviewSubmitting ? '处理中...' : '通过审核' }}
          </button>
          <button class="btn btn-danger" @click="rejectDoctorReview" :disabled="reviewSubmitting">
            {{ reviewSubmitting ? '处理中...' : '驳回申请' }}
          </button>
          <button class="btn btn-secondary" @click="closeDoctorReviewModal">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import axios from 'axios'
import * as adminApi from '@/api/admin'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('overview')

// 用户管理相关数据
const usersList = ref([])
const usersPage = ref(1)
const userSearchKeyword = ref('')
const showUserDetailModal = ref(false)
const selectedUserDetail = ref(null)
const loadingUserDetail = ref(false)

// 医生管理相关数据
const doctorsList = ref([])
const doctorsPage = ref(1)
const doctorFilter = ref('all')
const showDoctorReviewModal = ref(false)
const selectedDoctorReview = ref(null)
const reviewReason = ref('')
const reviewSubmitting = ref(false)

// 模拟数据
const totalUsers = ref(15847)
const newUsersThisMonth = ref(1256)
const verifiedDoctors = ref(89)
const pendingDoctors = ref(5)
const activeUsersToday = ref(2341)
const onlineUsers = ref(156)
const totalAppointments = ref(45623)
const todayAppointments = ref(127)

const systemHealth = ref({
  status: 'healthy',
  text: '系统运行正常',
  serverLoad: 35,
  dbStatus: 'healthy',
  apiResponseTime: 145,
  storageUsage: 67
})

const pendingTasks = ref([
  {
    id: 1,
    type: 'doctor-review',
    title: '医生资质审核',
    subtitle: '李医生 - 心内科主治医师',
    submitTime: '2小时前',
    priority: 'high'
  },
  {
    id: 2,
    type: 'content-review',
    title: '健康资讯审核',
    subtitle: '春季养生保健指南',
    submitTime: '4小时前',
    priority: 'medium'
  },
  {
    id: 3,
    type: 'doctor-review',
    title: '医生资质审核',
    subtitle: '张医生 - 儿科副主任医师',
    submitTime: '1天前',
    priority: 'medium'
  },
  {
    id: 4,
    type: 'user-report',
    title: '用户举报处理',
    subtitle: '不当言论举报',
    submitTime: '2天前',
    priority: 'low'
  }
])

const userInfo = computed(() => userStore.userInfo)

// 方法
const setActiveTab = (tab) => {
  activeTab.value = tab

  // 根据标签页加载相应数据
  if (tab === 'users') {
    loadUsers()
  } else if (tab === 'doctors') {
    loadDoctors()
  }
}

const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN')
}

const refreshData = async () => {
  console.log('刷新数据...')
  await loadDashboardData()
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    const response = await axios.get('/api/admin/statistics/overview', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    if (response.data.success) {
      const stats = response.data.data
      totalUsers.value = stats.totalUsers || 0
      verifiedDoctors.value = stats.totalDoctors || 0
      pendingDoctors.value = stats.pendingDoctors || 0
      totalAppointments.value = stats.totalAppointments || 0
      newUsersThisMonth.value = stats.monthNewUsers || 0
      todayAppointments.value = stats.todayAppointments || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    const response = await adminApi.getUsers({
      page: usersPage.value,
      size: 10,
      keyword: userSearchKeyword.value || undefined
    })

    if (response.data.code === 200) {
      usersList.value = response.data.data.content || []
    } else {
      console.error('加载用户列表失败:', response.data.message)
      alert('加载用户列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    alert('加载用户列表失败，请稍后重试')
  }
}

// 加载医生列表
const loadDoctors = async () => {
  try {
    const response = await adminApi.getDoctors({
      page: doctorsPage.value,
      size: 10,
      status: doctorFilter.value === 'all' ? undefined : doctorFilter.value
    })

    if (response.data.code === 200) {
      doctorsList.value = response.data.data.content || []
    } else {
      console.error('加载医生列表失败:', response.data.message)
      alert('加载医生列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('加载医生列表失败:', error)
    alert('加载医生列表失败，请稍后重试')
  }
}

const getPageTitle = () => {
  const titles = {
    users: '用户管理',
    doctors: '医生管理',
    content: '内容管理',
    announcements: '系统公告',
    settings: '系统设置'
  }
  return titles[activeTab.value] || '页面'
}

const getTaskTypeText = (type) => {
  const typeMap = {
    'doctor-review': '医生审核',
    'content-review': '内容审核',
    'user-report': '用户举报'
  }
  return typeMap[type] || type
}

const getPriorityText = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || priority
}

const approveTask = (task) => {
  console.log('批准任务:', task)
  // 这里将来连接API
}

const rejectTask = (task) => {
  console.log('拒绝任务:', task)
  // 这里将来连接API
}

const editTask = (task) => {
  console.log('编辑任务:', task)
  // 这里将来连接API
}

const viewTask = (task) => {
  console.log('查看任务详情:', task)
  // 这里将来连接API
}

const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}

// 用户管理方法
const searchUsers = () => {
  usersPage.value = 1
  loadUsers()
}

const prevUsersPage = () => {
  if (usersPage.value > 1) {
    usersPage.value--
    loadUsers()
  }
}

const nextUsersPage = () => {
  usersPage.value++
  loadUsers()
}

const viewUserDetail = async (user) => {
  try {
    loadingUserDetail.value = true
    const response = await adminApi.getUserDetail(user.id)

    if (response.data.code === 200) {
      selectedUserDetail.value = response.data.data
      showUserDetailModal.value = true
    } else {
      alert('获取用户详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    alert('获取用户详情失败，请稍后重试')
  } finally {
    loadingUserDetail.value = false
  }
}

const closeUserDetailModal = () => {
  showUserDetailModal.value = false
  selectedUserDetail.value = null
}

const toggleUserStatus = async (user) => {
  const newStatus = user.statusCode === 'active' ? 'DISABLED' : 'ACTIVE'
  const action = newStatus === 'ACTIVE' ? '启用' : '禁用'

  if (!confirm(`确定要${action}该用户吗？`)) return

  try {
    const response = await adminApi.updateUserStatus(user.id, newStatus)

    if (response.data.code === 200) {
      alert(response.data.message || `用户${action}成功`)
      loadUsers() // 重新加载用户列表
    } else {
      alert(`${action}用户失败: ` + response.data.message)
    }
  } catch (error) {
    console.error(`${action}用户失败:`, error)
    alert(`${action}用户失败，请稍后重试`)
  }
}

const resetPassword = async (userId) => {
  if (!confirm('确定要重置该用户的密码吗？')) return

  try {
    const response = await adminApi.resetUserPassword(userId)

    if (response.data.code === 200) {
      alert(response.data.message || '密码重置成功')
    } else {
      alert('重置密码失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    alert('重置密码失败，请稍后重试')
  }
}

const deleteUser = async (userId) => {
  if (!confirm('确定要删除该用户吗？此操作不可恢复！')) return

  try {
    const response = await adminApi.deleteUser(userId)

    if (response.data.code === 200) {
      alert(response.data.message || '用户删除成功')
      loadUsers()
    } else {
      alert('删除用户失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    alert('删除用户失败，请稍后重试')
  }
}

// 医生管理方法
const setDoctorFilter = (filter) => {
  doctorFilter.value = filter
  doctorsPage.value = 1
  loadDoctors()
}

const prevDoctorsPage = () => {
  if (doctorsPage.value > 1) {
    doctorsPage.value--
    loadDoctors()
  }
}

const nextDoctorsPage = () => {
  doctorsPage.value++
  loadDoctors()
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待审核',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'DISABLED': '已禁用'
  }
  return statusMap[status] || status
}

// 医生审核相关方法
const reviewDoctor = async (doctor) => {
  try {
    const response = await adminApi.getDoctorReviewDetail(doctor.userId)

    if (response.data.code === 200) {
      selectedDoctorReview.value = response.data.data
      reviewReason.value = ''
      showDoctorReviewModal.value = true
    } else {
      alert('获取医生详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取医生详情失败:', error)
    alert('获取医生详情失败，请稍后重试')
  }
}

const closeDoctorReviewModal = () => {
  showDoctorReviewModal.value = false
  selectedDoctorReview.value = null
  reviewReason.value = ''
  reviewSubmitting.value = false
}

const approveDoctorReview = async () => {
  if (!selectedDoctorReview.value) return

  if (!confirm('确定要通过该医生的审核吗？')) return

  try {
    reviewSubmitting.value = true
    const response = await adminApi.reviewDoctor(selectedDoctorReview.value.userId, {
      status: 'APPROVED',
      reason: reviewReason.value || '审核通过'
    })

    if (response.data.code === 200) {
      alert(response.data.message || '医生审核通过')
      closeDoctorReviewModal()
      loadDoctors() // 重新加载医生列表
    } else {
      alert('审核失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('审核失败:', error)
    alert('审核失败，请稍后重试')
  } finally {
    reviewSubmitting.value = false
  }
}

const rejectDoctorReview = async () => {
  if (!selectedDoctorReview.value) return

  if (!reviewReason.value.trim()) {
    alert('驳回申请时必须填写审核理由')
    return
  }

  if (!confirm('确定要驳回该医生的申请吗？')) return

  try {
    reviewSubmitting.value = true
    const response = await adminApi.reviewDoctor(selectedDoctorReview.value.userId, {
      status: 'REJECTED',
      reason: reviewReason.value
    })

    if (response.data.code === 200) {
      alert(response.data.message || '医生申请已驳回')
      closeDoctorReviewModal()
      loadDoctors() // 重新加载医生列表
    } else {
      alert('驳回失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('驳回失败:', error)
    alert('驳回失败，请稍后重试')
  } finally {
    reviewSubmitting.value = false
  }
}

const viewDoctorDetail = (doctor) => {
  // 查看医生详情（非审核）
  reviewDoctor(doctor)
}

const editDoctorInfo = (doctor) => {
  console.log('编辑医生信息:', doctor)
  // TODO: 实现医生信息编辑功能
  alert('医生信息编辑功能开发中...')
}

const disableDoctor = async (doctorUserId) => {
  if (!confirm('确定要禁用该医生吗？')) return

  try {
    const response = await adminApi.updateDoctorStatus(doctorUserId, 'DISABLED')

    if (response.data.code === 200) {
      alert(response.data.message || '医生已禁用')
      loadDoctors()
    } else {
      alert('禁用失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('禁用医生失败:', error)
    alert('禁用医生失败，请稍后重试')
  }
}

const enableDoctor = async (doctorUserId) => {
  if (!confirm('确定要启用该医生吗？')) return

  try {
    const response = await adminApi.updateDoctorStatus(doctorUserId, 'APPROVED')

    if (response.data.code === 200) {
      alert(response.data.message || '医生已启用')
      loadDoctors()
    } else {
      alert('启用失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('启用医生失败:', error)
    alert('启用医生失败，请稍后重试')
  }
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未知'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时初始化数据
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: #f0f2f5;
}

/* 管理员侧边栏 - 深蓝色权威主题 */
.admin-sidebar {
  width: 320px;
  background: linear-gradient(180deg, #001529 0%, #002140 50%, #001529 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
  box-shadow: 6px 0 24px rgba(0, 0, 0, 0.15);
}

.sidebar-header {
  padding: 28px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 36px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo h2 {
  color: white;
  font-size: 22px;
  font-weight: 700;
  margin: 0;
  letter-spacing: 0.5px;
}

.admin-badge {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  border: 1px solid rgba(255, 77, 79, 0.3);
  padding: 6px 16px;
  border-radius: 16px;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.badge-text {
  color: white;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
}

.sidebar-nav {
  flex: 1;
  padding: 24px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  margin: 3px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
  transform: translateX(8px);
}

.nav-icon {
  font-size: 22px;
  width: 28px;
  text-align: center;
}

.nav-item span {
  font-size: 16px;
  letter-spacing: 0.3px;
}

.pending-badge {
  background: #ff4d4f;
  color: white;
  font-size: 11px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 14px;
  margin-left: auto;
  min-width: 22px;
  text-align: center;
  animation: glow 2s infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 8px rgba(255, 77, 79, 0.6); }
  50% { box-shadow: 0 0 16px rgba(255, 77, 79, 0.8); }
}

.sidebar-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.admin-avatar {
  width: 52px;
  height: 52px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.admin-details {
  flex: 1;
}

.admin-name {
  font-weight: 700;
  color: white;
  font-size: 16px;
  margin-bottom: 4px;
}

.admin-role {
  color: #8c8c8c;
  font-size: 13px;
  margin-bottom: 2px;
}

.admin-status {
  color: #52c41a;
  font-size: 12px;
  font-weight: 600;
}

.logout-btn {
  width: 100%;
  padding: 14px 20px;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.2);
  border-radius: 10px;
  color: #ff7875;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(255, 77, 79, 0.2);
  border-color: rgba(255, 77, 79, 0.4);
  color: #ff4d4f;
  transform: translateY(-2px);
}

.logout-icon {
  font-size: 18px;
}

/* 主内容区 */
.admin-main {
  flex: 1;
  margin-left: 320px;
  padding: 28px;
  min-height: 100vh;
  background: #f0f2f5;
}

.overview-content {
  max-width: 1600px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8e8e8;
}

.page-header h1 {
  color: #262626;
  font-size: 36px;
  font-weight: 700;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.last-update {
  color: #8c8c8c;
  font-size: 14px;
}

.refresh-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.refresh-icon {
  font-size: 16px;
}

/* 核心数据指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 28px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.metric-card.primary {
  color: #1890ff;
}

.metric-card.success {
  color: #52c41a;
}

.metric-card.warning {
  color: #faad14;
}

.metric-card.info {
  color: #722ed1;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.metric-icon {
  font-size: 32px;
  padding: 12px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 12px;
}

.metric-card.success .metric-icon {
  background: rgba(82, 196, 26, 0.1);
}

.metric-card.warning .metric-icon {
  background: rgba(250, 173, 20, 0.1);
}

.metric-card.info .metric-icon {
  background: rgba(114, 46, 209, 0.1);
}

.metric-trend {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-trend.up {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-trend.stable {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-content {
  text-align: left;
}

.metric-number {
  font-size: 42px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 8px;
  line-height: 1;
}

.metric-label {
  color: #595959;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.metric-detail {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
}

/* 仪表盘网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  margin-bottom: 32px;
}

.system-health-card,
.pending-tasks-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.card-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.card-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.health-status {
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
}

.health-status.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.task-summary {
  background: #1890ff;
  color: white;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
}

.card-content {
  padding: 24px 28px;
}

/* 系统健康状态 */
.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 10px;
  border-left: 4px solid #1890ff;
}

.health-label {
  color: #595959;
  font-weight: 600;
  font-size: 14px;
}

.health-value {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #262626;
}

.progress-bar {
  width: 80px;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #1890ff);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.healthy {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
}

/* 待办事项表格 */
.tasks-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 100px 2fr 120px 80px 140px;
  gap: 16px;
  padding: 16px 20px;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 12px;
  font-weight: 700;
  font-size: 13px;
  color: #595959;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-row {
  display: grid;
  grid-template-columns: 100px 2fr 120px 80px 140px;
  gap: 16px;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
  transition: all 0.2s ease;
}

.table-row:hover {
  background: #fafafa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.high {
  border-left: 4px solid #ff4d4f;
  background: rgba(255, 77, 79, 0.02);
}

.table-row.medium {
  border-left: 4px solid #faad14;
}

.table-row.low {
  border-left: 4px solid #52c41a;
}

.task-type-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.task-type-badge.doctor-review {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.task-type-badge.content-review {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.task-type-badge.user-report {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.task-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  margin-bottom: 4px;
}

.task-subtitle {
  color: #8c8c8c;
  font-size: 13px;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
}

.priority-badge.high {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.priority-badge.medium {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.priority-badge.low {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.col-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  border: none;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.approve {
  background: #52c41a;
  color: white;
}

.action-btn.approve:hover {
  background: #389e0d;
  transform: translateY(-1px);
}

.action-btn.reject {
  background: #ff4d4f;
  color: white;
}

.action-btn.reject:hover {
  background: #d9363e;
  transform: translateY(-1px);
}

.action-btn.edit {
  background: #1890ff;
  color: white;
}

.action-btn.edit:hover {
  background: #096dd9;
  transform: translateY(-1px);
}

.action-btn.view {
  background: #f0f0f0;
  color: #595959;
}

.action-btn.view:hover {
  background: #d9d9d9;
  color: #262626;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.chart-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.chart-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.chart-controls select {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  color: #595959;
  cursor: pointer;
}

.chart-content {
  padding: 40px 28px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #8c8c8c;
  font-size: 18px;
  font-weight: 600;
}

.chart-note {
  color: #bfbfbf;
  font-size: 14px;
  font-weight: 400;
  margin-top: 12px;
}

/* 其他页面内容 */
.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.coming-soon {
  text-align: center;
  padding: 60px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.coming-soon-icon {
  font-size: 80px;
  margin-bottom: 24px;
}

.coming-soon h2 {
  color: #262626;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
}

.coming-soon p {
  color: #8c8c8c;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 80px 1fr 100px 60px 120px;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-main {
    margin-left: 0;
    padding: 20px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-header h1 {
    font-size: 28px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .metrics-grid {
    gap: 16px;
  }

  .metric-card {
    padding: 20px;
  }

  .dashboard-grid {
    gap: 16px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    margin-bottom: 12px;
    border: none;
  }

  .col-actions {
    justify-content: flex-start;
  }

  .health-metrics {
    gap: 16px;
  }

  .health-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .admin-main {
    padding: 16px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-number {
    font-size: 32px;
  }

  .card-content {
    padding: 16px 20px;
  }

  .chart-content {
    padding: 24px 20px;
    min-height: 200px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 11px;
  }
}

/* 数据表格样式 */
.data-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin: 20px 0;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e9ecef;
}

.data-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
}

.data-table tr:hover {
  background: #f8f9fa;
}

/* 按钮样式 */
.btn-small {
  padding: 6px 12px;
  margin: 0 4px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.doctor {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-badge.pending {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.approved {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.rejected {
  background: #ffebee;
  color: #c62828;
}

.status-badge.active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.disabled {
  background: #ffebee;
  color: #c62828;
}

/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.search-box input {
  border: none;
  padding: 10px 16px;
  outline: none;
  flex: 1;
}

.search-box button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 16px;
  cursor: pointer;
}

.search-box button:hover {
  background: #0056b3;
}

/* 过滤标签样式 */
.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-tab:hover {
  background: #f8f9fa;
}

.filter-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.filter-tab .badge {
  background: #dc3545;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  min-width: 16px;
  text-align: center;
}

.filter-tab.active .badge {
  background: rgba(255, 255, 255, 0.3);
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 用户详情弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.user-detail-modal {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.detail-item span {
  color: #333;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #2c5aa0;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.profiles-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.profile-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2c5aa0;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.profile-name {
  font-weight: 500;
  color: #333;
}

.profile-info {
  font-size: 14px;
  color: #666;
}

.profile-history {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.no-data {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: right;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-secondary:hover {
  background: #5a6268;
}

/* 医生审核弹窗样式 */
.doctor-review-modal {
  width: 95%;
  max-width: 900px;
  max-height: 95vh;
  overflow-y: auto;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.photo-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.photo-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.cert-photo {
  width: 100%;
  max-width: 200px;
  height: auto;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.cert-photo:hover {
  transform: scale(1.05);
  border-color: #2c5aa0;
}

.bio-text {
  margin: 8px 0 0 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  line-height: 1.5;
  color: #333;
}

.full-width {
  grid-column: 1 / -1;
}

.review-reason-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.review-reason-input:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.btn-success {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 医生管理标签页样式 */
.sub-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.sub-tab-btn {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.sub-tab-btn.active {
  color: #2c5aa0;
  border-bottom-color: #2c5aa0;
  font-weight: 500;
}

.sub-tab-btn:hover {
  color: #2c5aa0;
}

.sub-tab-content {
  margin-top: 20px;
}

.filter-tabs {
  display: flex;
  gap: 10px;
}

.filter-tab {
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #2c5aa0;
  color: white;
  border-color: #2c5aa0;
}

.filter-tab:hover {
  background: #e9ecef;
}

.filter-tab.active:hover {
  background: #1e3d6f;
}

.badge {
  background: #dc3545;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
}
</style>
