package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ContentRepository extends JpaRepository<Content, Long> {
    
    /**
     * 根据作者ID查找内容
     */
    Page<Content> findByAuthorId(Long authorId, Pageable pageable);
    
    /**
     * 根据作者ID和内容类型查找内容
     */
    Page<Content> findByAuthorIdAndContentType(Long authorId, Content.ContentType contentType, Pageable pageable);
    
    /**
     * 根据内容类型查找内容
     */
    Page<Content> findByContentType(Content.ContentType contentType, Pageable pageable);
    
    /**
     * 统计作者的内容数量
     */
    long countByAuthorId(Long authorId);
    
    /**
     * 统计作者指定类型的内容数量
     */
    long countByAuthorIdAndContentType(Long authorId, Content.ContentType contentType);
    
    /**
     * 统计作者在指定时间范围内的内容数量
     */
    long countByAuthorIdAndPublishedAtBetween(Long authorId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计作者在指定时间范围内指定类型的内容数量
     */
    long countByAuthorIdAndContentTypeAndPublishedAtBetween(Long authorId, Content.ContentType contentType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找作者在指定时间范围内的内容
     */
    List<Content> findByAuthorIdAndPublishedAtBetween(Long authorId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找作者在指定时间范围内指定类型的内容
     */
    List<Content> findByAuthorIdAndContentTypeAndPublishedAtBetween(Long authorId, Content.ContentType contentType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找所有已发布的内容（按发布时间倒序）
     */
    Page<Content> findByPublishedAtIsNotNullOrderByPublishedAtDesc(Pageable pageable);

    /**
     * 根据内容类型查找已发布的内容（按发布时间倒序）
     */
    Page<Content> findByContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(Content.ContentType contentType, Pageable pageable);

    /**
     * 根据标题关键词搜索内容
     */
    @Query("SELECT c FROM Content c WHERE c.title LIKE %:keyword% AND c.publishedAt IS NOT NULL ORDER BY c.publishedAt DESC")
    Page<Content> findByTitleContainingAndPublishedAtIsNotNull(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找即将到来的活动（活动时间在未来）
     */
    @Query("SELECT c FROM Content c WHERE c.contentType = 'ACTIVITY' AND c.activityTime > :now AND c.publishedAt IS NOT NULL ORDER BY c.activityTime ASC")
    Page<Content> findUpcomingActivities(@Param("now") LocalDateTime now, Pageable pageable);

    /**
     * 查找已结束的活动（活动时间在过去）
     */
    @Query("SELECT c FROM Content c WHERE c.contentType = 'ACTIVITY' AND c.activityTime < :now AND c.publishedAt IS NOT NULL ORDER BY c.activityTime DESC")
    Page<Content> findPastActivities(@Param("now") LocalDateTime now, Pageable pageable);

    /**
     * 根据作者ID和内容类型查找已发布的内容（按发布时间倒序）
     */
    Page<Content> findByAuthorIdAndContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(Long authorId, Content.ContentType contentType, Pageable pageable);
}
