package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.OnlineConsultation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OnlineConsultationRepository extends JpaRepository<OnlineConsultation, Long> {
    
    /**
     * 根据医生ID查找问诊记录
     */
    Page<OnlineConsultation> findByDoctorId(Long doctorId, Pageable pageable);
    
    /**
     * 根据医生ID和状态查找问诊记录
     */
    Page<OnlineConsultation> findByDoctorIdAndStatus(Long doctorId, OnlineConsultation.ConsultationStatus status, Pageable pageable);
    
    /**
     * 根据用户ID查找问诊记录
     */
    Page<OnlineConsultation> findByUserId(Long userId, Pageable pageable);

    /**
     * 统计用户的问诊数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计医生的问诊数量
     */
    long countByDoctorId(Long doctorId);
    
    /**
     * 统计医生指定状态的问诊数量
     */
    long countByDoctorIdAndStatus(Long doctorId, OnlineConsultation.ConsultationStatus status);
    
    /**
     * 统计医生在指定时间范围内的问诊数量
     */
    long countByDoctorIdAndCreatedAtBetween(Long doctorId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计医生在指定时间范围内指定状态的问诊数量
     */
    long countByDoctorIdAndStatusAndCreatedAtBetween(Long doctorId, OnlineConsultation.ConsultationStatus status, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找医生在指定时间范围内的问诊记录
     */
    List<OnlineConsultation> findByDoctorIdAndCreatedAtBetween(Long doctorId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找医生在指定时间范围内指定状态的问诊记录
     */
    List<OnlineConsultation> findByDoctorIdAndStatusAndCreatedAtBetween(Long doctorId, OnlineConsultation.ConsultationStatus status, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 按日期统计医生的问诊数量
     */
    @Query("SELECT DATE(c.createdAt), COUNT(c) FROM OnlineConsultation c " +
           "WHERE c.doctorId = :doctorId AND c.status = :status " +
           "AND c.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(c.createdAt) ORDER BY DATE(c.createdAt)")
    List<Object[]> countByDoctorIdAndStatusGroupByDate(@Param("doctorId") Long doctorId, 
                                                      @Param("status") OnlineConsultation.ConsultationStatus status,
                                                      @Param("startTime") LocalDateTime startTime, 
                                                      @Param("endTime") LocalDateTime endTime);
}
