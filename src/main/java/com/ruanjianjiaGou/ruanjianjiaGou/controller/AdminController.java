package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final AdminService adminService;
    private final UserRepository userRepository;

    /**
     * 获取管理员统计信息
     */
    @GetMapping("/statistics/overview")
    public Result<Map<String, Object>> getDashboardStatistics(Authentication authentication) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getDashboardStatistics(user.getId());
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<org.springframework.data.domain.Page<Map<String, Object>>> getUsers(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            org.springframework.data.domain.Page<Map<String, Object>> users = adminService.getUsers(user.getId(), page, size, keyword);
            return Result.success(users);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取医生列表
     */
    @GetMapping("/doctors")
    public Result<org.springframework.data.domain.Page<Map<String, Object>>> getDoctors(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            org.springframework.data.domain.Page<Map<String, Object>> doctors = adminService.getDoctors(user.getId(), page, size, status);
            return Result.success(doctors);
        } catch (Exception e) {
            log.error("获取医生列表失败", e);
            return Result.error("获取医生列表失败：" + e.getMessage());
        }
    }

    /**
     * 创建医生记录
     */
    @PostMapping("/doctors")
    public Result<Map<String, Object>> createDoctor(
            Authentication authentication,
            @RequestBody Map<String, Object> doctorData) {
        try {
            // 从JWT Token中获取手机号，然后查找用户ID
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> doctor = adminService.createDoctor(user.getId(), doctorData);
            return Result.success(doctor);
        } catch (Exception e) {
            log.error("创建医生记录失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新医生状态（审核）
     */
    @PutMapping("/doctors/{doctorUserId}/status")
    public Result<String> updateDoctorStatus(
            Authentication authentication,
            @PathVariable Long doctorUserId,
            @RequestBody Map<String, String> statusData) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String status = statusData.get("status");
            adminService.updateDoctorStatus(user.getId(), doctorUserId, status);
            return Result.success("医生状态更新成功");
        } catch (Exception e) {
            log.error("更新医生状态失败", e);
            return Result.error("更新医生状态失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{userId}")
    public Result<String> deleteUser(
            Authentication authentication,
            @PathVariable Long userId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            adminService.deleteUser(user.getId(), userId);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/users/{userId}/reset-password")
    public Result<String> resetUserPassword(
            Authentication authentication,
            @PathVariable Long userId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String newPassword = adminService.resetUserPassword(user.getId(), userId);
            return Result.success("密码重置成功，新密码：" + newPassword);
        } catch (Exception e) {
            log.error("重置密码失败", e);
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }

    /**
     * 获取健康数据统计
     */
    @GetMapping("/statistics/health")
    public Result<Map<String, Object>> getHealthStatistics(
            Authentication authentication,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getHealthStatistics(user.getId(), startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取健康统计失败", e);
            return Result.error("获取健康统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取预约统计
     */
    @GetMapping("/statistics/appointments")
    public Result<Map<String, Object>> getAppointmentStatistics(
            Authentication authentication,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getAppointmentStatistics(user.getId(), startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取预约统计失败", e);
            return Result.error("获取预约统计失败：" + e.getMessage());
        }
    }
}

