package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.service.AdminService;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.UserDetailVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.DoctorReviewVO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.DoctorReviewDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentUpdateDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final AdminService adminService;
    private final UserRepository userRepository;

    /**
     * 获取管理员统计信息
     */
    @GetMapping("/statistics/overview")
    public Result<Map<String, Object>> getDashboardStatistics(Authentication authentication) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getDashboardStatistics(user.getId());
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<org.springframework.data.domain.Page<Map<String, Object>>> getUsers(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            org.springframework.data.domain.Page<Map<String, Object>> users = adminService.getUsers(user.getId(), page, size, keyword);
            return Result.success(users);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取医生列表
     */
    @GetMapping("/doctors")
    public Result<org.springframework.data.domain.Page<Map<String, Object>>> getDoctors(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            org.springframework.data.domain.Page<Map<String, Object>> doctors = adminService.getDoctors(user.getId(), page, size, status);
            return Result.success(doctors);
        } catch (Exception e) {
            log.error("获取医生列表失败", e);
            return Result.error("获取医生列表失败：" + e.getMessage());
        }
    }

    /**
     * 创建医生记录
     */
    @PostMapping("/doctors")
    public Result<Map<String, Object>> createDoctor(
            Authentication authentication,
            @RequestBody Map<String, Object> doctorData) {
        try {
            // 从JWT Token中获取手机号，然后查找用户ID
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> doctor = adminService.createDoctor(user.getId(), doctorData);
            return Result.success(doctor);
        } catch (Exception e) {
            log.error("创建医生记录失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新医生状态（审核）- 简单版本，保持兼容性
     */
    @PutMapping("/doctors/{doctorUserId}/status")
    public Result<String> updateDoctorStatus(
            Authentication authentication,
            @PathVariable Long doctorUserId,
            @RequestBody Map<String, String> statusData) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String status = statusData.get("status");
            adminService.updateDoctorStatus(user.getId(), doctorUserId, status);
            return Result.success("医生状态更新成功");
        } catch (Exception e) {
            log.error("更新医生状态失败", e);
            return Result.error("更新医生状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取医生审核详情
     */
    @GetMapping("/doctors/{doctorUserId}/review")
    public Result<DoctorReviewVO> getDoctorReviewDetail(
            Authentication authentication,
            @PathVariable Long doctorUserId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            DoctorReviewVO reviewDetail = adminService.getDoctorReviewDetail(user.getId(), doctorUserId);
            return Result.success(reviewDetail);
        } catch (Exception e) {
            log.error("获取医生审核详情失败", e);
            return Result.error("获取医生审核详情失败：" + e.getMessage());
        }
    }

    /**
     * 审核医生申请
     */
    @PutMapping("/doctors/{doctorUserId}/review")
    public Result<String> reviewDoctor(
            Authentication authentication,
            @PathVariable Long doctorUserId,
            @RequestBody DoctorReviewDTO reviewDTO) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            adminService.reviewDoctor(user.getId(), doctorUserId, reviewDTO);

            String message = "APPROVED".equals(reviewDTO.getStatus()) ? "医生审核通过" : "医生审核驳回";
            return Result.success(message);
        } catch (Exception e) {
            log.error("审核医生失败", e);
            return Result.error("审核医生失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{userId}")
    public Result<String> deleteUser(
            Authentication authentication,
            @PathVariable Long userId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            adminService.deleteUser(user.getId(), userId);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/users/{userId}/reset-password")
    public Result<String> resetUserPassword(
            Authentication authentication,
            @PathVariable Long userId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String newPassword = adminService.resetUserPassword(user.getId(), userId);
            return Result.success("密码重置成功，新密码：" + newPassword);
        } catch (Exception e) {
            log.error("重置密码失败", e);
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/users/{userId}")
    public Result<UserDetailVO> getUserDetail(
            Authentication authentication,
            @PathVariable Long userId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            UserDetailVO userDetail = adminService.getUserDetail(user.getId(), userId);
            return Result.success(userDetail);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/users/{userId}/status")
    public Result<String> updateUserStatus(
            Authentication authentication,
            @PathVariable Long userId,
            @RequestBody Map<String, String> statusData) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            String statusStr = statusData.get("status");
            User.UserStatus status = User.UserStatus.valueOf(statusStr.toUpperCase());
            adminService.updateUserStatus(user.getId(), userId, status);

            String message = status == User.UserStatus.ACTIVE ? "用户已启用" : "用户已禁用";
            return Result.success(message);
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取健康数据统计
     */
    @GetMapping("/statistics/health")
    public Result<Map<String, Object>> getHealthStatistics(
            Authentication authentication,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getHealthStatistics(user.getId(), startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取健康统计失败", e);
            return Result.error("获取健康统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取预约统计
     */
    @GetMapping("/statistics/appointments")
    public Result<Map<String, Object>> getAppointmentStatistics(
            Authentication authentication,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> statistics = adminService.getAppointmentStatistics(user.getId(), startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取预约统计失败", e);
            return Result.error("获取预约统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取内容列表
     */
    @GetMapping("/contents")
    public Result<org.springframework.data.domain.Page<Map<String, Object>>> getContents(
            Authentication authentication,
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            org.springframework.data.domain.Page<Map<String, Object>> contents = adminService.getContents(user.getId(), type, page, size);
            return Result.success(contents);
        } catch (Exception e) {
            log.error("获取内容列表失败", e);
            return Result.error("获取内容列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取内容详情
     */
    @GetMapping("/contents/{contentId}")
    public Result<Map<String, Object>> getContentDetail(
            Authentication authentication,
            @PathVariable Long contentId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> content = adminService.getContentDetail(user.getId(), contentId);
            return Result.success(content);
        } catch (Exception e) {
            log.error("获取内容详情失败", e);
            return Result.error("获取内容详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建内容
     */
    @PostMapping("/contents")
    public Result<Map<String, Object>> createContent(
            Authentication authentication,
            @RequestBody ContentCreateDTO createDTO) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> content = adminService.createContent(user.getId(), createDTO);
            return Result.success(content);
        } catch (Exception e) {
            log.error("创建内容失败", e);
            return Result.error("创建内容失败：" + e.getMessage());
        }
    }

    /**
     * 更新内容
     */
    @PutMapping("/contents/{contentId}")
    public Result<Map<String, Object>> updateContent(
            Authentication authentication,
            @PathVariable Long contentId,
            @RequestBody ContentUpdateDTO updateDTO) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> content = adminService.updateContent(user.getId(), contentId, updateDTO);
            return Result.success(content);
        } catch (Exception e) {
            log.error("更新内容失败", e);
            return Result.error("更新内容失败：" + e.getMessage());
        }
    }

    /**
     * 删除内容
     */
    @DeleteMapping("/contents/{contentId}")
    public Result<String> deleteContent(
            Authentication authentication,
            @PathVariable Long contentId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            adminService.deleteContent(user.getId(), contentId);
            return Result.success("内容删除成功");
        } catch (Exception e) {
            log.error("删除内容失败", e);
            return Result.error("删除内容失败：" + e.getMessage());
        }
    }
}

