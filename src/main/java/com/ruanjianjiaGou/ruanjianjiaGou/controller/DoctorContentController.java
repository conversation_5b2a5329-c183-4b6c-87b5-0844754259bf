package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentDetailDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentListDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.service.ContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 医生端内容管理控制器
 */
@RestController
@RequestMapping("/api/doctor/contents")
@RequiredArgsConstructor
@Slf4j
public class DoctorContentController {

    private final ContentService contentService;
    private final UserRepository userRepository;

    /**
     * 获取医生发布的健康指导列表
     */
    @GetMapping("/guidance")
    public Result<Page<ContentListDTO>> getDoctorGuidance(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long doctorId = getCurrentUserId(authentication);
            Page<ContentListDTO> guidance = contentService.getDoctorGuidanceByAuthor(doctorId, page, size);
            return Result.success(guidance);
        } catch (Exception e) {
            log.error("获取医生健康指导失败", e);
            return Result.error("获取健康指导失败：" + e.getMessage());
        }
    }

    /**
     * 创建健康指导
     */
    @PostMapping("/guidance")
    public Result<ContentDetailDTO> createGuidance(
            Authentication authentication,
            @Valid @RequestBody ContentCreateDTO createDTO) {
        try {
            Long doctorId = getCurrentUserId(authentication);
            String userRole = getUserRole(authentication);
            
            // 强制设置为健康指导类型
            createDTO.setContentType(com.ruanjianjiaGou.ruanjianjiaGou.entity.Content.ContentType.GUIDANCE);
            
            ContentDetailDTO content = contentService.createContent(doctorId, userRole, createDTO);
            return Result.success(content);
        } catch (Exception e) {
            log.error("创建健康指导失败", e);
            return Result.error("创建健康指导失败：" + e.getMessage());
        }
    }

    /**
     * 更新健康指导
     */
    @PutMapping("/guidance/{contentId}")
    public Result<ContentDetailDTO> updateGuidance(
            Authentication authentication,
            @PathVariable Long contentId,
            @Valid @RequestBody ContentUpdateDTO updateDTO) {
        try {
            Long doctorId = getCurrentUserId(authentication);
            String userRole = getUserRole(authentication);
            
            ContentDetailDTO content = contentService.updateContent(contentId, doctorId, userRole, updateDTO);
            return Result.success(content);
        } catch (Exception e) {
            log.error("更新健康指导失败", e);
            return Result.error("更新健康指导失败：" + e.getMessage());
        }
    }

    /**
     * 删除健康指导
     */
    @DeleteMapping("/guidance/{contentId}")
    public Result<String> deleteGuidance(
            Authentication authentication,
            @PathVariable Long contentId) {
        try {
            Long doctorId = getCurrentUserId(authentication);
            String userRole = getUserRole(authentication);
            
            contentService.deleteContent(contentId, doctorId, userRole);
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("删除健康指导失败", e);
            return Result.error("删除健康指导失败：" + e.getMessage());
        }
    }

    /**
     * 获取健康指导详情
     */
    @GetMapping("/guidance/{contentId}")
    public Result<ContentDetailDTO> getGuidanceDetail(
            Authentication authentication,
            @PathVariable Long contentId) {
        try {
            Long doctorId = getCurrentUserId(authentication);
            String userRole = getUserRole(authentication);
            
            ContentDetailDTO content = contentService.getContentDetail(contentId, doctorId, userRole);
            return Result.success(content);
        } catch (Exception e) {
            log.error("获取健康指导详情失败", e);
            return Result.error("获取健康指导详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        String phoneNumber = authentication.getName();
        User user = userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return user.getId();
    }

    /**
     * 获取当前用户角色
     */
    private String getUserRole(Authentication authentication) {
        String phoneNumber = authentication.getName();
        User user = userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return user.getRole().name();
    }
}
