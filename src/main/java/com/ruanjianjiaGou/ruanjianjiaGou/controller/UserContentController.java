package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentDetailDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentListDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.service.ContentService;
import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 用户端内容控制器
 */
@RestController
@RequestMapping("/api/user/contents")
@RequiredArgsConstructor
@Slf4j
public class UserContentController {

    private final ContentService contentService;
    private final UserRepository userRepository;

    /**
     * 获取活动列表
     */
    @GetMapping("/activities")
    public Result<Page<ContentListDTO>> getActivities(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long currentUserId = getCurrentUserId(authentication);
            Page<ContentListDTO> activities = contentService.getActivitiesForUser(currentUserId, page, size);
            return Result.success(activities);
        } catch (Exception e) {
            log.error("获取活动列表失败", e);
            return Result.error("获取活动列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取健康资讯列表
     */
    @GetMapping("/news")
    public Result<Page<ContentListDTO>> getHealthNews(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long currentUserId = getCurrentUserId(authentication);
            Page<ContentListDTO> news = contentService.getHealthNewsForUser(currentUserId, page, size);
            return Result.success(news);
        } catch (Exception e) {
            log.error("获取健康资讯失败", e);
            return Result.error("获取健康资讯失败：" + e.getMessage());
        }
    }

    /**
     * 获取医生指导列表
     */
    @GetMapping("/guidance")
    public Result<Page<ContentListDTO>> getDoctorGuidance(
            Authentication authentication,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long currentUserId = getCurrentUserId(authentication);
            Page<ContentListDTO> guidance = contentService.getDoctorGuidanceForUser(currentUserId, page, size);
            return Result.success(guidance);
        } catch (Exception e) {
            log.error("获取医生指导失败", e);
            return Result.error("获取医生指导失败：" + e.getMessage());
        }
    }

    /**
     * 获取内容详情
     */
    @GetMapping("/{contentId}")
    public Result<ContentDetailDTO> getContentDetail(
            Authentication authentication,
            @PathVariable Long contentId) {
        try {
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            ContentDetailDTO content = contentService.getContentDetail(contentId, user.getId(), user.getRole().name());
            return Result.success(content);
        } catch (Exception e) {
            log.error("获取内容详情失败", e);
            return Result.error("获取内容详情失败：" + e.getMessage());
        }
    }

    /**
     * 报名参加活动
     */
    @PostMapping("/activities/{activityId}/register")
    public Result<String> registerActivity(
            Authentication authentication,
            @PathVariable Long activityId) {
        try {
            Long currentUserId = getCurrentUserId(authentication);
            contentService.registerActivity(activityId, currentUserId);
            return Result.success("报名成功");
        } catch (Exception e) {
            log.error("活动报名失败", e);
            return Result.error("活动报名失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        String phoneNumber = authentication.getName();
        User user = userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return user.getId();
    }
}
