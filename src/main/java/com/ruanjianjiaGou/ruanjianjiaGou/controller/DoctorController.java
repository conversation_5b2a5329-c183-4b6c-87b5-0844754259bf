package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.DoctorScheduleDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.AppointmentDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription.PrescriptionCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription.PrescriptionListDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.service.PrescriptionService;
import com.ruanjianjiaGou.ruanjianjiaGou.service.DoctorService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 医生专用控制器
 */
@RestController
@RequestMapping("/api/doctor")
@RequiredArgsConstructor
@Slf4j
public class DoctorController {
    
    private final DoctorService doctorService;
    private final PrescriptionService prescriptionService;
    private final SecurityUtils securityUtils;
    
    /**
     * 查看我的排班
     */
    @GetMapping("/schedules/my")
    public Result<List<DoctorScheduleDTO>> getMySchedules(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            List<DoctorScheduleDTO> schedules = doctorService.getDoctorSchedules(userId, startDate, endDate);
            return Result.success("获取成功", schedules);
        } catch (Exception e) {
            log.error("获取医生排班失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 创建排班
     */
    @PostMapping("/schedules")
    public Result<DoctorScheduleDTO> createSchedule(
            @Valid @RequestBody DoctorScheduleDTO scheduleDTO,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            DoctorScheduleDTO schedule = doctorService.createSchedule(userId, scheduleDTO);
            return Result.success("排班创建成功", schedule);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("创建排班失败", e);
            return Result.error("创建失败，请稍后重试");
        }
    }
    
    /**
     * 更新排班
     */
    @PutMapping("/schedules/{id}")
    public Result<Void> updateSchedule(
            @PathVariable Long id,
            @Valid @RequestBody DoctorScheduleDTO scheduleDTO,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            doctorService.updateSchedule(userId, id, scheduleDTO);
            return Result.success("排班更新成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("更新排班失败", e);
            return Result.error("更新失败，请稍后重试");
        }
    }
    
    /**
     * 删除排班
     */
    @DeleteMapping("/schedules/{id}")
    public Result<Void> deleteSchedule(
            @PathVariable Long id,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            doctorService.deleteSchedule(userId, id);
            return Result.success("排班删除成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("删除排班失败", e);
            return Result.error("删除失败，请稍后重试");
        }
    }
    
    /**
     * 我的预约患者
     */
    @GetMapping("/appointments/my")
    public Result<Page<AppointmentDTO>> getMyAppointments(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Page<AppointmentDTO> appointments = doctorService.getDoctorAppointments(userId, page, size, status);
            return Result.success("获取成功", appointments);
        } catch (Exception e) {
            log.error("获取医生预约失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 确认预约
     */
    @PostMapping("/appointments/{id}/confirm")
    public Result<Void> confirmAppointment(
            @PathVariable Long id,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            doctorService.confirmAppointment(userId, id);
            return Result.success("预约确认成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("确认预约失败", e);
            return Result.error("确认失败，请稍后重试");
        }
    }
    
    /**
     * 完成诊疗
     */
    @PostMapping("/appointments/{id}/complete")
    public Result<Void> completeAppointment(
            @PathVariable Long id,
            @RequestBody(required = false) Map<String, String> notes,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String treatmentNotes = notes != null ? notes.get("notes") : null;
            doctorService.completeAppointment(userId, id, treatmentNotes);
            return Result.success("诊疗完成");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("完成诊疗失败", e);
            return Result.error("操作失败，请稍后重试");
        }
    }
    
    /**
     * 添加诊疗记录
     */
    @PostMapping("/appointments/{id}/record")
    public Result<Void> addTreatmentRecord(
            @PathVariable Long id,
            @RequestBody Map<String, String> record,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            doctorService.addTreatmentRecord(userId, id, record);
            return Result.success("诊疗记录添加成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("添加诊疗记录失败", e);
            return Result.error("添加失败，请稍后重试");
        }
    }
    
    /**
     * 获取患者病历
     */
    @GetMapping("/patients/{id}/records")
    public Result<List<Map<String, Object>>> getPatientRecords(
            @PathVariable Long id,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            List<Map<String, Object>> records = doctorService.getPatientRecords(userId, id);
            return Result.success("获取成功", records);
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("获取患者病历失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 我的工作统计
     */
    @GetMapping("/statistics/my")
    public Result<Map<String, Object>> getMyStatistics(HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Map<String, Object> statistics = doctorService.getDoctorStatistics(userId);
            return Result.success("获取成功", statistics);
        } catch (Exception e) {
            log.error("获取医生统计失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 患者健康数据
     */
    @GetMapping("/patients/{id}/health-data")
    public Result<Map<String, Object>> getPatientHealthData(
            @PathVariable Long id,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Map<String, Object> healthData = doctorService.getPatientHealthData(userId, id);
            return Result.success("获取成功", healthData);
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("获取患者健康数据失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 生成诊疗报告
     */
    @GetMapping("/reports/treatment")
    public Result<Map<String, Object>> generateTreatmentReport(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Map<String, Object> report = doctorService.generateTreatmentReport(userId, startDate, endDate);
            return Result.success("报告生成成功", report);
        } catch (Exception e) {
            log.error("生成诊疗报告失败", e);
            return Result.error("生成失败，请稍后重试");
        }
    }

    /**
     * 批量操作预约
     */
    @PostMapping("/appointments/batch")
    public Result<Void> batchOperateAppointments(
            @RequestBody Map<String, Object> batchData,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            doctorService.batchOperateAppointments(userId, batchData);
            return Result.success("批量操作成功");
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("批量操作失败", e);
            return Result.error("批量操作失败，请稍后重试");
        }
    }

    /**
     * 取消预约
     */
    @PostMapping("/appointments/{id}/cancel")
    public Result<Void> cancelAppointment(
            @PathVariable Long id,
            @RequestBody(required = false) Map<String, String> reason,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String cancelReason = reason != null ? reason.get("reason") : null;
            doctorService.cancelAppointment(userId, id, cancelReason);
            return Result.success("预约取消成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            log.error("取消预约失败", e);
            return Result.error("取消失败，请稍后重试");
        }
    }

    /**
     * 获取预约统计信息
     */
    @GetMapping("/appointments/stats")
    public Result<Map<String, Object>> getAppointmentStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Map<String, Object> stats = doctorService.getAppointmentStats(userId, startDate, endDate);
            return Result.success("获取统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取预约统计失败", e);
            return Result.error("获取统计信息失败，请稍后重试");
        }
    }

    // ==================== 预约诊断处方接口 ====================

    /**
     * 为预约创建诊断和处方
     * 医生可以对任何预约进行诊断，无时间限制
     */
    @PostMapping("/appointments/{appointmentId}/prescribe")
    public Result<Map<String, Object>> createPrescriptionForAppointment(
            @PathVariable Long appointmentId,
            @Valid @RequestBody PrescriptionCreateDTO prescriptionDTO,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);

            // 设置预约ID到处方DTO中
            prescriptionDTO.setAppointmentId(appointmentId);

            // 创建处方（包含诊断信息）
            PrescriptionListDTO prescription = prescriptionService.createPrescriptionForAppointment(userId, prescriptionDTO);

            Map<String, Object> result = new HashMap<>();
            result.put("prescription", prescription);
            result.put("message", "诊断和处方创建成功");

            return Result.success("诊断处方创建成功", result);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("创建诊断处方失败", e);
            return Result.error("创建失败，请稍后重试");
        }
    }

    /**
     * 获取预约的诊断处方记录
     */
    @GetMapping("/appointments/{appointmentId}/prescription")
    public Result<PrescriptionListDTO> getPrescriptionByAppointment(
            @PathVariable Long appointmentId,
            HttpServletRequest request) {
        try {
            PrescriptionListDTO prescription = prescriptionService.getPrescriptionByAppointmentId(appointmentId.intValue());
            if (prescription == null) {
                return Result.success("暂无诊断处方记录", null);
            }
            return Result.success("获取成功", prescription);
        } catch (Exception e) {
            log.error("获取诊断处方记录失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }

    /**
     * 获取我的诊断处方记录列表
     */
    @GetMapping("/prescriptions/my")
    public Result<Page<PrescriptionListDTO>> getMyPrescriptions(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Page<PrescriptionListDTO> prescriptions = prescriptionService.getDoctorPrescriptions(userId, page, size);
            return Result.success("获取成功", prescriptions);
        } catch (Exception e) {
            log.error("获取诊断处方记录列表失败", e);
            return Result.error("获取失败，请稍后重试");
        }
    }
}
