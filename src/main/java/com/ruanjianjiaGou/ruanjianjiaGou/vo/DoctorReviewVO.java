package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Doctor;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 医生审核详情VO - 用于管理端审核医生申请
 */
@Data
public class DoctorReviewVO {
    
    /**
     * 基本信息
     */
    private Long userId;
    private String phoneNumber;
    private String nickname;
    private String realName;
    private Gender gender;
    private LocalDate birthDate;
    private Integer age;
    private String idCardNumber;
    private String avatarUrl;
    
    /**
     * 专业信息
     */
    private Long departmentId;
    private String departmentName;
    private String title;
    private String specialty;
    private String bio;
    
    /**
     * 审核相关信息
     */
    private Doctor.DoctorStatus status;
    private String statusDescription;
    private String idCardPhotoUrl;
    private String qualificationPhotoUrl;
    private String licenseNumber;
    private LocalDateTime applicationTime;
    private String reviewReason;
    private LocalDateTime reviewedAt;
    private Long reviewedBy;
    private String reviewerName;
    
    /**
     * 统计信息
     */
    private Integer appointmentCount; // 预约次数
    private Integer consultationCount; // 咨询次数
    private Integer prescriptionCount; // 处方数量
}
