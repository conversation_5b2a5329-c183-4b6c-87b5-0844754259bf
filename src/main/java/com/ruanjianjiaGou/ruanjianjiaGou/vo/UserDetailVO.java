package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详情VO - 用于管理端查看用户详细信息
 */
@Data
public class UserDetailVO {
    
    /**
     * 用户基本信息
     */
    private Long id;
    private String phoneNumber;
    private String nickname;
    private User.UserRole role;
    private User.UserStatus status;
    private LocalDateTime createdAt;
    
    /**
     * 角色描述
     */
    private String roleDescription;
    private String statusDescription;
    
    /**
     * 统计信息
     */
    private Integer healthProfileCount; // 健康档案数量
    private Integer appointmentCount;   // 预约次数
    private Integer consultationCount;  // 咨询次数
    
    /**
     * 健康档案列表
     */
    private List<HealthProfileSummaryVO> healthProfiles;
    
    /**
     * 健康档案摘要VO
     */
    @Data
    public static class HealthProfileSummaryVO {
        private Long id;
        private String profileOwnerName;
        private String gender;
        private Integer age;
        private String medicalHistory;
    }
}
