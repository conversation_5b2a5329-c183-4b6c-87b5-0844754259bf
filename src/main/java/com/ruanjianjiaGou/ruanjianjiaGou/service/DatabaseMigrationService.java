package com.ruanjianjiaGou.ruanjianjiaGou.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 数据库迁移服务
 * 用于处理数据库结构的更新和修复
 */
@Service
@Slf4j
public class DatabaseMigrationService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 应用启动后执行数据库迁移
     */
    @EventListener(ApplicationReadyEvent.class)
    public void performDatabaseMigration() {
        log.info("开始执行数据库迁移...");
        
        try {
            // 检查并添加appointment_id字段到e_prescriptions表
            addAppointmentIdToPrescriptions();
            
            log.info("数据库迁移完成");
        } catch (Exception e) {
            log.error("数据库迁移失败", e);
        }
    }

    /**
     * 为e_prescriptions表添加appointment_id字段
     */
    private void addAppointmentIdToPrescriptions() {
        try {
            // 检查appointment_id字段是否已存在
            String checkColumnSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                    "WHERE TABLE_SCHEMA = 'community_health_db' " +
                    "AND TABLE_NAME = 'e_prescriptions' " +
                    "AND COLUMN_NAME = 'appointment_id'";
            
            Integer columnExists = jdbcTemplate.queryForObject(checkColumnSql, Integer.class);
            
            if (columnExists == null || columnExists == 0) {
                log.info("添加appointment_id字段到e_prescriptions表...");
                
                // 添加appointment_id字段
                String addColumnSql = "ALTER TABLE e_prescriptions " +
                        "ADD COLUMN appointment_id INT UNSIGNED NULL COMMENT '关联的预约ID' " +
                        "AFTER consultation_id";
                jdbcTemplate.execute(addColumnSql);
                
                // 添加索引
                String addIndexSql = "ALTER TABLE e_prescriptions " +
                        "ADD INDEX fk_prescriptions_appointments_idx (appointment_id ASC)";
                jdbcTemplate.execute(addIndexSql);
                
                log.info("appointment_id字段添加成功");
            } else {
                log.info("appointment_id字段已存在，跳过添加");
            }
            
        } catch (Exception e) {
            log.error("添加appointment_id字段失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
