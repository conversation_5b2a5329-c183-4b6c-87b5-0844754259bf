package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.UserDetailVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.DoctorReviewVO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.DoctorReviewDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.ContentUpdateDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdminService {
    
    private final UserRepository userRepository;
    private final DoctorRepository doctorRepository;
    private final DepartmentRepository departmentRepository;
    private final HealthProfileRepository healthProfileRepository;
    private final HealthMetricRecordRepository healthRecordRepository;
    private final AppointmentRepository appointmentRepository;
    private final OnlineConsultationRepository consultationRepository;
    private final EPrescriptionRepository prescriptionRepository;
    private final ContentRepository contentRepository;
    private final PasswordEncoder passwordEncoder;
    
    /**
     * 验证管理员权限
     */
    private void validateAdmin(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 基于role字段验证管理员权限
        if (!user.isAdmin()) {
            throw new RuntimeException("无管理员权限");
        }
    }
    
    /**
     * 获取管理员统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getDashboardStatistics(Long userId) {
        validateAdmin(userId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 基础统计
        long totalUsers = userRepository.countByRole(User.UserRole.RESIDENT);
        long totalDoctors = doctorRepository.countByStatus(Doctor.DoctorStatus.APPROVED);
        long pendingDoctors = doctorRepository.countByStatus(Doctor.DoctorStatus.PENDING);
        long totalAppointments = appointmentRepository.count();

        // 今日新增统计
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime todayEnd = todayStart.plusDays(1);
        long todayNewUsers = userRepository.countByCreatedAtBetween(todayStart, todayEnd);
        long todayAppointments = appointmentRepository.countByCreatedAtBetween(todayStart, todayEnd);

        // 本月新增统计
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long monthNewUsers = userRepository.countByCreatedAtBetween(monthStart, LocalDateTime.now());

        statistics.put("totalUsers", totalUsers);
        statistics.put("totalDoctors", totalDoctors);
        statistics.put("pendingDoctors", pendingDoctors);
        statistics.put("totalAppointments", totalAppointments);
        statistics.put("todayNewUsers", todayNewUsers);
        statistics.put("todayAppointments", todayAppointments);
        statistics.put("monthNewUsers", monthNewUsers);
        
        return statistics;
    }
    
    /**
     * 获取用户列表（只显示居民用户）
     */
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> getUsers(Long userId, int page, int size, String keyword) {
        validateAdmin(userId);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));

        Page<User> users;
        if (keyword != null && !keyword.trim().isEmpty()) {
            // 搜索居民用户
            users = userRepository.findByRoleAndPhoneNumberContainingOrRoleAndNicknameContaining(
                    User.UserRole.RESIDENT, keyword,
                    User.UserRole.RESIDENT, keyword,
                    pageable);
        } else {
            // 只获取居民用户
            users = userRepository.findByRole(User.UserRole.RESIDENT, pageable);
        }

        return users.map(this::convertUserToMap);
    }
    
    /**
     * 获取医生列表
     */
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> getDoctors(Long userId, int page, int size, String status) {
        validateAdmin(userId);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "userId"));
        
        Page<Doctor> doctors;
        if (status != null && !status.trim().isEmpty()) {
            Doctor.DoctorStatus doctorStatus = Doctor.DoctorStatus.valueOf(status.toUpperCase());
            doctors = doctorRepository.findByStatus(doctorStatus, pageable);
        } else {
            doctors = doctorRepository.findAll(pageable);
        }
        
        return doctors.map(this::convertDoctorToMap);
    }
    
    /**
     * 创建医生记录
     */
    @Transactional
    public Map<String, Object> createDoctor(Long userId, Map<String, Object> doctorData) {
        validateAdmin(userId);
        
        Long doctorUserId = Long.parseLong(doctorData.get("userId").toString());
        String realName = (String) doctorData.get("realName");
        Long departmentId = Long.parseLong(doctorData.get("departmentId").toString());
        String title = (String) doctorData.get("title");
        String specialty = (String) doctorData.get("specialty");
        String bio = (String) doctorData.get("bio");
        
        // 验证用户存在
        User user = userRepository.findById(doctorUserId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        // 验证科室存在
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("科室不存在"));
        
        // 检查是否已经是医生（跳过有枚举值问题的记录）
        try {
            if (doctorRepository.findByUserId(doctorUserId).isPresent()) {
                throw new RuntimeException("该用户已经是医生");
            }
        } catch (Exception e) {
            // 如果查询失败（可能是因为枚举值问题），继续创建医生记录
            log.warn("查询医生记录时出现问题，可能是数据库中存在旧的状态值: {}", e.getMessage());
        }
        
        Doctor doctor = new Doctor();
        doctor.setUserId(doctorUserId);
        doctor.setRealName(realName);
        doctor.setDepartmentId(departmentId);
        doctor.setTitle(title);
        doctor.setSpecialty(specialty);
        doctor.setBio(bio);
        doctor.setStatus(Doctor.DoctorStatus.APPROVED);
        
        doctor = doctorRepository.save(doctor);
        return convertDoctorToMap(doctor);
    }
    
    /**
     * 更新医生状态（简单版本，保持兼容性）
     */
    @Transactional
    public void updateDoctorStatus(Long userId, Long doctorUserId, String status) {
        validateAdmin(userId);

        Doctor doctor = doctorRepository.findByUserId(doctorUserId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));

        Doctor.DoctorStatus doctorStatus = Doctor.DoctorStatus.valueOf(status.toUpperCase());
        doctor.setStatus(doctorStatus);
        doctorRepository.save(doctor);
    }

    /**
     * 获取医生审核详情
     */
    @Transactional(readOnly = true)
    public DoctorReviewVO getDoctorReviewDetail(Long adminId, Long doctorUserId) {
        validateAdmin(adminId);

        Doctor doctor = doctorRepository.findByUserId(doctorUserId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));

        DoctorReviewVO reviewVO = new DoctorReviewVO();

        // 基本信息
        User user = doctor.getUser();
        if (user != null) {
            reviewVO.setUserId(user.getId());
            reviewVO.setPhoneNumber(user.getPhoneNumber());
            reviewVO.setNickname(user.getNickname());
        }

        reviewVO.setRealName(doctor.getRealName());
        reviewVO.setGender(doctor.getGender());
        reviewVO.setBirthDate(doctor.getBirthDate());
        reviewVO.setAge(doctor.getAge());
        reviewVO.setIdCardNumber(doctor.getIdCardNumber());
        reviewVO.setAvatarUrl(doctor.getAvatarUrl());

        // 专业信息
        reviewVO.setDepartmentId(doctor.getDepartmentId());
        if (doctor.getDepartment() != null) {
            reviewVO.setDepartmentName(doctor.getDepartment().getName());
        }
        reviewVO.setTitle(doctor.getTitle());
        reviewVO.setSpecialty(doctor.getSpecialty());
        reviewVO.setBio(doctor.getBio());

        // 审核信息
        reviewVO.setStatus(doctor.getStatus());
        reviewVO.setStatusDescription(doctor.getStatus().getDescription());
        reviewVO.setIdCardPhotoUrl(doctor.getIdCardPhotoUrl());
        reviewVO.setQualificationPhotoUrl(doctor.getQualificationPhotoUrl());
        reviewVO.setLicenseNumber(doctor.getLicenseNumber());
        reviewVO.setApplicationTime(doctor.getApplicationTime());
        reviewVO.setReviewReason(doctor.getReviewReason());
        reviewVO.setReviewedAt(doctor.getReviewedAt());
        reviewVO.setReviewedBy(doctor.getReviewedBy());

        // 获取审核人姓名
        if (doctor.getReviewedBy() != null) {
            userRepository.findById(doctor.getReviewedBy())
                    .ifPresent(reviewer -> reviewVO.setReviewerName(reviewer.getNickname()));
        }

        // 统计信息
        long appointmentCount = appointmentRepository.countByDoctorId(doctorUserId);
        reviewVO.setAppointmentCount((int) appointmentCount);

        long consultationCount = consultationRepository.countByDoctorId(doctorUserId);
        reviewVO.setConsultationCount((int) consultationCount);

        // 处方数量统计
        long prescriptionCount = prescriptionRepository.countByDoctorId(doctorUserId);
        reviewVO.setPrescriptionCount((int) prescriptionCount);

        return reviewVO;
    }

    /**
     * 审核医生申请
     */
    @Transactional
    public void reviewDoctor(Long adminId, Long doctorUserId, DoctorReviewDTO reviewDTO) {
        validateAdmin(adminId);

        Doctor doctor = doctorRepository.findByUserId(doctorUserId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));

        // 只能审核待审核状态的医生
        if (doctor.getStatus() != Doctor.DoctorStatus.PENDING) {
            throw new RuntimeException("该医生已经审核过了，当前状态：" + doctor.getStatus().getDescription());
        }

        Doctor.DoctorStatus newStatus = Doctor.DoctorStatus.valueOf(reviewDTO.getStatus().toUpperCase());
        doctor.setStatus(newStatus);
        doctor.setReviewReason(reviewDTO.getReason());
        doctor.setReviewedAt(java.time.LocalDateTime.now());
        doctor.setReviewedBy(adminId);

        doctorRepository.save(doctor);

        log.info("医生审核完成: doctorUserId={}, status={}, reviewedBy={}",
                doctorUserId, newStatus, adminId);
    }

    /**
     * 获取内容列表（按类型筛选）
     */
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> getContents(Long adminId, String type, int page, int size) {
        validateAdmin(adminId);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));

        Page<Content> contents;
        if (type != null && !type.equals("all")) {
            try {
                // 直接尝试转换为枚举，如果失败则处理小写情况
                Content.ContentType contentType;
                if (type.equals("news")) {
                    contentType = Content.ContentType.NEWS;
                } else if (type.equals("activity")) {
                    contentType = Content.ContentType.ACTIVITY;
                } else if (type.equals("guidance")) {
                    contentType = Content.ContentType.GUIDANCE;
                } else {
                    // 尝试直接转换大写
                    contentType = Content.ContentType.valueOf(type.toUpperCase());
                }
                contents = contentRepository.findByContentType(contentType, pageable);
            } catch (IllegalArgumentException e) {
                log.warn("无效的内容类型: {}", type);
                // 如果类型无效，返回空结果
                contents = contentRepository.findByContentType(Content.ContentType.NEWS, PageRequest.of(0, 0));
            }
        } else {
            contents = contentRepository.findAll(pageable);
        }

        return contents.map(this::convertContentToMap);
    }

    /**
     * 创建内容
     */
    @Transactional
    public Map<String, Object> createContent(Long adminId, ContentCreateDTO createDTO) {
        validateAdmin(adminId);

        Content content = new Content();
        content.setAuthorId(adminId);
        content.setContentType(createDTO.getContentType());
        content.setTitle(createDTO.getTitle());
        content.setBody(createDTO.getBody());
        content.setPublishedAt(LocalDateTime.now());

        // 如果是活动类型，设置活动相关信息
        if (createDTO.getContentType() == Content.ContentType.ACTIVITY) {
            content.setActivityTime(createDTO.getActivityTime());
            content.setActivityLocation(createDTO.getActivityLocation());
        }

        content = contentRepository.save(content);
        log.info("管理员创建内容成功，ID: {}, 类型: {}", content.getId(), content.getContentType());

        return convertContentToMap(content);
    }

    /**
     * 更新内容
     */
    @Transactional
    public Map<String, Object> updateContent(Long adminId, Long contentId, ContentUpdateDTO updateDTO) {
        validateAdmin(adminId);

        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));

        content.setTitle(updateDTO.getTitle());
        content.setBody(updateDTO.getBody());

        // 如果是活动类型，更新活动相关信息
        if (content.getContentType() == Content.ContentType.ACTIVITY) {
            content.setActivityTime(updateDTO.getActivityTime());
            content.setActivityLocation(updateDTO.getActivityLocation());
        }

        content = contentRepository.save(content);
        log.info("管理员更新内容成功，ID: {}", content.getId());

        return convertContentToMap(content);
    }

    /**
     * 删除内容
     */
    @Transactional
    public void deleteContent(Long adminId, Long contentId) {
        validateAdmin(adminId);

        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));

        contentRepository.delete(content);
        log.info("管理员删除内容成功，ID: {}", contentId);
    }

    /**
     * 获取内容详情
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getContentDetail(Long adminId, Long contentId) {
        validateAdmin(adminId);

        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));

        return convertContentToMap(content);
    }
    
    /**
     * 获取健康数据统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getHealthStatistics(Long userId, String startDate, String endDate) {
        validateAdmin(userId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 健康档案统计
        long totalProfiles = healthProfileRepository.count();
        statistics.put("totalProfiles", totalProfiles);
        
        // 健康记录统计
        long totalRecords = healthRecordRepository.count();
        statistics.put("totalRecords", totalRecords);
        
        // 按指标类型统计
        List<Object[]> metricStats = healthRecordRepository.countByMetricType();
        Map<String, Long> metricCounts = new HashMap<>();
        for (Object[] stat : metricStats) {
            metricCounts.put((String) stat[0], (Long) stat[1]);
        }
        statistics.put("metricCounts", metricCounts);
        
        return statistics;
    }
    
    /**
     * 获取预约统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getAppointmentStatistics(Long userId, String startDate, String endDate) {
        validateAdmin(userId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 预约总数
        long totalAppointments = appointmentRepository.count();
        statistics.put("totalAppointments", totalAppointments);
        
        // 按状态统计
        long bookedCount = appointmentRepository.countByStatus(Appointment.AppointmentStatus.BOOKED);
        long completedCount = appointmentRepository.countByStatus(Appointment.AppointmentStatus.COMPLETED);
        long cancelledCount = appointmentRepository.countByStatus(Appointment.AppointmentStatus.CANCELLED);
        
        statistics.put("bookedCount", bookedCount);
        statistics.put("completedCount", completedCount);
        statistics.put("cancelledCount", cancelledCount);
        
        return statistics;
    }
    
    /**
     * 获取系统日志
     */
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> getSystemLogs(Long userId, int page, int size, String level) {
        validateAdmin(userId);
        
        // 模拟系统日志数据
        List<Map<String, Object>> logs = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            Map<String, Object> log = new HashMap<>();
            log.put("id", (page - 1) * size + i + 1);
            log.put("level", level != null ? level : "INFO");
            log.put("message", "系统日志消息 " + ((page - 1) * size + i + 1));
            log.put("timestamp", LocalDateTime.now().minusHours(i));
            logs.add(log);
        }
        
        return new PageImpl<>(logs, PageRequest.of(page - 1, size), 100);
    }
    
    /**
     * 创建公告
     */
    @Transactional
    public Map<String, Object> createAnnouncement(Long userId, Map<String, Object> announcementData) {
        validateAdmin(userId);
        
        Map<String, Object> announcement = new HashMap<>();
        announcement.put("id", System.currentTimeMillis());
        announcement.put("title", announcementData.get("title"));
        announcement.put("content", announcementData.get("content"));
        announcement.put("createdAt", LocalDateTime.now());
        announcement.put("authorId", userId);
        
        return announcement;
    }
    
    /**
     * 获取公告列表
     */
    @Transactional(readOnly = true)
    public Page<Map<String, Object>> getAnnouncements(Long userId, int page, int size) {
        validateAdmin(userId);
        
        // 模拟公告数据
        List<Map<String, Object>> announcements = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            Map<String, Object> announcement = new HashMap<>();
            announcement.put("id", (page - 1) * size + i + 1);
            announcement.put("title", "系统公告 " + ((page - 1) * size + i + 1));
            announcement.put("content", "这是一条系统公告内容");
            announcement.put("createdAt", LocalDateTime.now().minusDays(i));
            announcements.add(announcement);
        }
        
        return new PageImpl<>(announcements, PageRequest.of(page - 1, size), 50);
    }
    
    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long adminId, Long userId) {
        validateAdmin(adminId);
        
        if (userId.equals(adminId)) {
            throw new RuntimeException("不能删除自己");
        }
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        // 检查是否是医生，如果是先删除医生记录
        doctorRepository.findByUserId(userId).ifPresent(doctorRepository::delete);
        
        userRepository.delete(user);
    }
    
    /**
     * 重置用户密码
     */
    @Transactional
    public String resetUserPassword(Long adminId, Long userId) {
        validateAdmin(adminId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        String newPassword = "123456"; // 默认密码
        user.setPassword(newPassword); // 明文密码
        userRepository.save(user);

        return newPassword;
    }

    /**
     * 获取用户详细信息
     */
    @Transactional(readOnly = true)
    public UserDetailVO getUserDetail(Long adminId, Long userId) {
        validateAdmin(adminId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        UserDetailVO userDetail = new UserDetailVO();

        // 基本信息
        userDetail.setId(user.getId());
        userDetail.setPhoneNumber(user.getPhoneNumber());
        userDetail.setNickname(user.getNickname());
        userDetail.setRole(user.getRole());
        userDetail.setStatus(user.getStatus() != null ? user.getStatus() : User.UserStatus.ACTIVE);
        userDetail.setCreatedAt(user.getCreatedAt());
        userDetail.setRoleDescription(user.getRole().getDescription());
        userDetail.setStatusDescription(user.getStatus() != null ? user.getStatus().getDescription() : "正常");

        // 获取健康档案信息
        List<HealthProfile> healthProfiles = healthProfileRepository.findByManagingUserId(userId);
        userDetail.setHealthProfileCount(healthProfiles.size());

        // 转换健康档案信息
        List<UserDetailVO.HealthProfileSummaryVO> profileSummaries = healthProfiles.stream()
                .map(this::convertToProfileSummary)
                .collect(Collectors.toList());
        userDetail.setHealthProfiles(profileSummaries);

        // 统计预约次数
        long appointmentCount = appointmentRepository.countByUserId(userId);
        userDetail.setAppointmentCount((int) appointmentCount);

        // 统计咨询次数
        long consultationCount = consultationRepository.countByUserId(userId);
        userDetail.setConsultationCount((int) consultationCount);

        return userDetail;
    }

    /**
     * 更新用户状态
     */
    @Transactional
    public void updateUserStatus(Long adminId, Long userId, User.UserStatus status) {
        validateAdmin(adminId);

        if (userId.equals(adminId)) {
            throw new RuntimeException("不能修改自己的状态");
        }

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setStatus(status);
        userRepository.save(user);
    }
    
    /**
     * 导出数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> exportData(Long userId, String dataType, String startDate, String endDate) {
        validateAdmin(userId);
        
        Map<String, Object> exportInfo = new HashMap<>();
        exportInfo.put("dataType", dataType);
        exportInfo.put("startDate", startDate);
        exportInfo.put("endDate", endDate);
        exportInfo.put("exportTime", LocalDateTime.now());
        exportInfo.put("fileName", dataType + "_export_" + System.currentTimeMillis() + ".csv");
        exportInfo.put("status", "success");
        
        return exportInfo;
    }
    
    // 辅助方法
    private Map<String, Object> convertUserToMap(User user) {
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("id", user.getId());
        userMap.put("phoneNumber", user.getPhoneNumber());
        userMap.put("nickname", user.getNickname());
        userMap.put("createdAt", user.getCreatedAt());
        userMap.put("role", user.getRole().getDescription());

        // 处理可能为null的status
        if (user.getStatus() != null) {
            userMap.put("status", user.getStatus().getDescription());
            userMap.put("statusCode", user.getStatus().getCode());
        } else {
            // 如果status为null，设置默认值为ACTIVE
            userMap.put("status", "正常");
            userMap.put("statusCode", "active");
        }

        // 检查是否是医生
        doctorRepository.findByUserId(user.getId()).ifPresent(doctor -> {
            userMap.put("isDoctor", true);
            userMap.put("doctorStatus", doctor.getStatus().name());
        });

        return userMap;
    }
    
    private Map<String, Object> convertDoctorToMap(Doctor doctor) {
        Map<String, Object> doctorMap = new HashMap<>();
        doctorMap.put("userId", doctor.getUserId());
        doctorMap.put("realName", doctor.getRealName());
        doctorMap.put("departmentId", doctor.getDepartmentId());
        doctorMap.put("title", doctor.getTitle());
        doctorMap.put("specialty", doctor.getSpecialty());
        doctorMap.put("bio", doctor.getBio());
        doctorMap.put("status", doctor.getStatus().name());
        
        if (doctor.getDepartment() != null) {
            doctorMap.put("departmentName", doctor.getDepartment().getName());
        }
        
        return doctorMap;
    }

    /**
     * 转换健康档案为摘要VO
     */
    private UserDetailVO.HealthProfileSummaryVO convertToProfileSummary(HealthProfile profile) {
        UserDetailVO.HealthProfileSummaryVO summary = new UserDetailVO.HealthProfileSummaryVO();
        summary.setId(profile.getId());
        summary.setProfileOwnerName(profile.getProfileOwnerName());
        summary.setGender(profile.getGender() != null ? profile.getGender().getDescription() : null);
        summary.setAge(profile.getAge());
        summary.setMedicalHistory(profile.getMedicalHistory());
        return summary;
    }

    /**
     * 转换内容为Map
     */
    private Map<String, Object> convertContentToMap(Content content) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("id", content.getId());
        contentMap.put("title", content.getTitle());
        contentMap.put("body", content.getBody());
        contentMap.put("contentType", content.getContentType());
        contentMap.put("contentTypeDescription", content.getContentType().getDescription());
        contentMap.put("publishedAt", content.getPublishedAt());
        contentMap.put("authorId", content.getAuthorId());

        // 获取作者信息
        if (content.getAuthor() != null) {
            contentMap.put("authorName", content.getAuthor().getNickname());
        } else {
            userRepository.findById(content.getAuthorId())
                    .ifPresent(author -> contentMap.put("authorName", author.getNickname()));
        }

        // 如果是活动类型，添加活动相关信息
        if (content.getContentType() == Content.ContentType.ACTIVITY) {
            contentMap.put("activityTime", content.getActivityTime());
            contentMap.put("activityLocation", content.getActivityLocation());
        }

        return contentMap;
    }
}

// 需要添加的PageImpl类
class PageImpl<T> implements Page<T> {
    private final List<T> content;
    private final Pageable pageable;
    private final long total;
    
    public PageImpl(List<T> content, Pageable pageable, long total) {
        this.content = content;
        this.pageable = pageable;
        this.total = total;
    }
    
    @Override
    public List<T> getContent() {
        return content;
    }
    
    @Override
    public int getTotalPages() {
        return (int) Math.ceil((double) total / pageable.getPageSize());
    }
    
    @Override
    public long getTotalElements() {
        return total;
    }
    
    @Override
    public boolean hasNext() {
        return pageable.getPageNumber() + 1 < getTotalPages();
    }
    
    @Override
    public boolean isLast() {
        return !hasNext();
    }
    
    @Override
    public boolean hasContent() {
        return !content.isEmpty();
    }
    
    @Override
    public boolean isFirst() {
        return pageable.getPageNumber() == 0;
    }
    
    @Override
    public boolean hasPrevious() {
        return pageable.getPageNumber() > 0;
    }
    
    @Override
    public Pageable nextPageable() {
        return hasNext() ? pageable.next() : Pageable.unpaged();
    }
    
    @Override
    public Pageable previousPageable() {
        return hasPrevious() ? pageable.previousOrFirst() : Pageable.unpaged();
    }
    
    @Override
    public int getSize() {
        return pageable.getPageSize();
    }
    
    @Override
    public int getNumber() {
        return pageable.getPageNumber();
    }
    
    @Override
    public int getNumberOfElements() {
        return content.size();
    }
    
    @Override
    public Sort getSort() {
        return pageable.getSort();
    }
    
    @Override
    public boolean isEmpty() {
        return content.isEmpty();
    }
    
    @Override
    public java.util.Iterator<T> iterator() {
        return content.iterator();
    }
    
    @Override
    public <U> Page<U> map(java.util.function.Function<? super T, ? extends U> converter) {
        List<U> convertedContent = content.stream().map(converter).collect(Collectors.toList());
        return new PageImpl<>(convertedContent, pageable, total);
    }
}
