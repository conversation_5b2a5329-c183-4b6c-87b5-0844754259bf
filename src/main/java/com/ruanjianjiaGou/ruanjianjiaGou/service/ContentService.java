package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 内容管理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ContentService {
    
    private final ContentRepository contentRepository;
    private final ActivityRegistrationRepository activityRegistrationRepository;
    private final UserRepository userRepository;
    private final DoctorRepository doctorRepository;
    
    /**
     * 创建内容
     */
    public ContentDetailDTO createContent(Long authorId, String userRole, ContentCreateDTO createDTO) {
        log.info("用户 {} (角色: {}) 创建内容，类型: {}", authorId, userRole, createDTO.getContentType());
        
        // 权限验证
        validateCreatePermission(userRole, createDTO.getContentType());
        
        // 验证作者是否存在
        User author = userRepository.findById(authorId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        // 如果是医生发布健康指导，验证医生状态
        if ("DOCTOR".equals(userRole) && createDTO.getContentType() == Content.ContentType.GUIDANCE) {
            Doctor doctor = doctorRepository.findByUserId(authorId)
                    .orElseThrow(() -> new RuntimeException("医生信息不存在"));
            if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
                throw new RuntimeException("医生尚未通过审核，无法发布健康指导");
            }
        }
        
        // 创建内容
        Content content = new Content();
        content.setAuthorId(authorId);
        content.setContentType(createDTO.getContentType());
        content.setTitle(createDTO.getTitle());
        content.setBody(createDTO.getBody());
        content.setPublishedAt(LocalDateTime.now());
        
        // 如果是活动类型，设置活动相关信息
        if (createDTO.getContentType() == Content.ContentType.ACTIVITY) {
            content.setActivityTime(createDTO.getActivityTime());
            content.setActivityLocation(createDTO.getActivityLocation());
        }
        
        content = contentRepository.save(content);
        log.info("内容创建成功，ID: {}", content.getId());
        
        return convertToContentDetailDTO(content, authorId, userRole);
    }
    
    /**
     * 更新内容
     */
    public ContentDetailDTO updateContent(Long contentId, Long currentUserId, String userRole, ContentUpdateDTO updateDTO) {
        log.info("用户 {} (角色: {}) 更新内容 {}", currentUserId, userRole, contentId);
        
        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));
        
        // 权限验证
        validateUpdatePermission(content, currentUserId, userRole);
        
        // 更新内容
        content.setTitle(updateDTO.getTitle());
        content.setBody(updateDTO.getBody());
        
        // 如果是活动类型，更新活动相关信息
        if (content.getContentType() == Content.ContentType.ACTIVITY) {
            content.setActivityTime(updateDTO.getActivityTime());
            content.setActivityLocation(updateDTO.getActivityLocation());
        }
        
        content = contentRepository.save(content);
        log.info("内容更新成功，ID: {}", content.getId());
        
        return convertToContentDetailDTO(content, currentUserId, userRole);
    }
    
    /**
     * 删除内容
     */
    public void deleteContent(Long contentId, Long currentUserId, String userRole) {
        log.info("用户 {} (角色: {}) 删除内容 {}", currentUserId, userRole, contentId);
        
        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));
        
        // 权限验证
        validateDeletePermission(content, currentUserId, userRole);
        
        contentRepository.delete(content);
        log.info("内容删除成功，ID: {}", contentId);
    }
    
    /**
     * 获取内容列表
     */
    @Transactional(readOnly = true)
    public Page<ContentListDTO> getContentList(Content.ContentType contentType, String keyword, Long currentUserId, int page, int size) {
        log.info("获取内容列表，类型: {}, 关键词: {}, 页码: {}, 大小: {}", contentType, keyword, page, size);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));
        Page<Content> contents;
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            // 关键词搜索
            contents = contentRepository.findByTitleContainingAndPublishedAtIsNotNull(keyword.trim(), pageable);
        } else if (contentType != null) {
            // 按类型查询
            contents = contentRepository.findByContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(contentType, pageable);
        } else {
            // 查询所有
            contents = contentRepository.findByPublishedAtIsNotNullOrderByPublishedAtDesc(pageable);
        }
        
        return contents.map(content -> convertToContentListDTO(content, currentUserId));
    }
    
    /**
     * 获取内容详情
     */
    @Transactional(readOnly = true)
    public ContentDetailDTO getContentDetail(Long contentId, Long currentUserId, String userRole) {
        log.info("获取内容详情，ID: {}, 当前用户: {}", contentId, currentUserId);
        
        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new RuntimeException("内容不存在"));
        
        return convertToContentDetailDTO(content, currentUserId, userRole);
    }
    
    /**
     * 报名参加活动
     */
    public void registerActivity(Long activityId, Long userId) {
        log.info("用户 {} 报名参加活动 {}", userId, activityId);
        
        // 验证活动是否存在且为活动类型
        Content activity = contentRepository.findById(activityId)
                .orElseThrow(() -> new RuntimeException("活动不存在"));
        
        if (activity.getContentType() != Content.ContentType.ACTIVITY) {
            throw new RuntimeException("该内容不是活动类型");
        }
        
        // 检查活动时间是否已过
        if (activity.getActivityTime() != null && activity.getActivityTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("活动已结束，无法报名");
        }
        
        // 检查是否已经报名
        if (activityRegistrationRepository.existsByIdActivityIdAndIdUserId(activityId, userId)) {
            throw new RuntimeException("您已经报名了该活动");
        }
        
        // 创建报名记录
        ActivityRegistration registration = new ActivityRegistration();
        ActivityRegistration.ActivityRegistrationId id = new ActivityRegistration.ActivityRegistrationId();
        id.setActivityId(activityId);
        id.setUserId(userId);
        registration.setId(id);
        registration.setRegisteredAt(LocalDateTime.now());
        
        activityRegistrationRepository.save(registration);
        log.info("活动报名成功，活动ID: {}, 用户ID: {}", activityId, userId);
    }
    
    /**
     * 验证创建权限
     */
    private void validateCreatePermission(String userRole, Content.ContentType contentType) {
        if ("ADMIN".equals(userRole)) {
            // 管理员可以创建所有类型的内容
            return;
        }
        
        if ("DOCTOR".equals(userRole) && contentType == Content.ContentType.GUIDANCE) {
            // 医生只能发布健康指导
            return;
        }
        
        throw new RuntimeException("无权限创建该类型的内容");
    }
    
    /**
     * 验证更新权限
     */
    private void validateUpdatePermission(Content content, Long currentUserId, String userRole) {
        if ("ADMIN".equals(userRole)) {
            // 管理员可以更新所有内容
            return;
        }
        
        if (content.getAuthorId().equals(currentUserId)) {
            // 内容创建者可以更新自己的内容
            return;
        }
        
        throw new RuntimeException("无权限更新该内容");
    }
    
    /**
     * 验证删除权限
     */
    private void validateDeletePermission(Content content, Long currentUserId, String userRole) {
        if ("ADMIN".equals(userRole)) {
            // 管理员可以删除所有内容
            return;
        }
        
        if (content.getAuthorId().equals(currentUserId)) {
            // 内容创建者可以删除自己的内容
            return;
        }
        
        throw new RuntimeException("无权限删除该内容");
    }

    /**
     * 转换为内容列表DTO
     */
    private ContentListDTO convertToContentListDTO(Content content, Long currentUserId) {
        ContentListDTO dto = new ContentListDTO();
        dto.setId(content.getId());
        dto.setAuthorId(content.getAuthorId());
        dto.setContentType(content.getContentType());
        dto.setContentTypeDescription(content.getContentType().getDescription());
        dto.setTitle(content.getTitle());
        dto.setPublishedAt(content.getPublishedAt());
        dto.setActivityTime(content.getActivityTime());
        dto.setActivityLocation(content.getActivityLocation());

        // 生成内容摘要（前100字符）
        if (content.getBody() != null) {
            String summary = content.getBody().length() > 100
                ? content.getBody().substring(0, 100) + "..."
                : content.getBody();
            dto.setSummary(summary);
        }

        // 获取作者信息
        userRepository.findById(content.getAuthorId()).ifPresent(author -> {
            dto.setAuthorName(author.getNickname());
        });

        // 如果是活动类型，获取报名信息
        if (content.getContentType() == Content.ContentType.ACTIVITY) {
            long registrationCount = activityRegistrationRepository.countByIdActivityId(content.getId());
            dto.setRegistrationCount(registrationCount);

            if (currentUserId != null) {
                boolean isRegistered = activityRegistrationRepository.existsByIdActivityIdAndIdUserId(content.getId(), currentUserId);
                dto.setIsRegistered(isRegistered);
            }
        }

        return dto;
    }

    /**
     * 转换为内容详情DTO
     */
    private ContentDetailDTO convertToContentDetailDTO(Content content, Long currentUserId, String userRole) {
        ContentDetailDTO dto = new ContentDetailDTO();
        dto.setId(content.getId());
        dto.setAuthorId(content.getAuthorId());
        dto.setContentType(content.getContentType());
        dto.setContentTypeDescription(content.getContentType().getDescription());
        dto.setTitle(content.getTitle());
        dto.setBody(content.getBody());
        dto.setPublishedAt(content.getPublishedAt());
        dto.setActivityTime(content.getActivityTime());
        dto.setActivityLocation(content.getActivityLocation());

        // 获取作者信息
        userRepository.findById(content.getAuthorId()).ifPresent(author -> {
            dto.setAuthorName(author.getNickname());

            // 如果作者是医生，获取医生信息
            if (author.getRole() == User.UserRole.DOCTOR) {
                doctorRepository.findByUserId(author.getId()).ifPresent(doctor -> {
                    dto.setAuthorTitle(doctor.getTitle());
                    if (doctor.getDepartment() != null) {
                        dto.setDepartmentName(doctor.getDepartment().getName());
                    }
                });
            }
        });

        // 如果是活动类型，获取报名信息
        if (content.getContentType() == Content.ContentType.ACTIVITY) {
            long registrationCount = activityRegistrationRepository.countByIdActivityId(content.getId());
            dto.setRegistrationCount(registrationCount);

            if (currentUserId != null) {
                boolean isRegistered = activityRegistrationRepository.existsByIdActivityIdAndIdUserId(content.getId(), currentUserId);
                dto.setIsRegistered(isRegistered);
            }
        }

        // 设置权限信息
        if (currentUserId != null) {
            boolean canEdit = "ADMIN".equals(userRole) || content.getAuthorId().equals(currentUserId);
            boolean canDelete = "ADMIN".equals(userRole) || content.getAuthorId().equals(currentUserId);
            dto.setCanEdit(canEdit);
            dto.setCanDelete(canDelete);
        }

        return dto;
    }

    /**
     * 获取活动列表（用户端）
     */
    @Transactional(readOnly = true)
    public Page<ContentListDTO> getActivitiesForUser(Long currentUserId, int page, int size) {
        log.info("获取活动列表，用户: {}, 页码: {}, 大小: {}", currentUserId, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));
        Page<Content> activities = contentRepository.findByContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(
                Content.ContentType.ACTIVITY, pageable);

        return activities.map(content -> convertToContentListDTO(content, currentUserId));
    }

    /**
     * 获取健康资讯列表（用户端）
     */
    @Transactional(readOnly = true)
    public Page<ContentListDTO> getHealthNewsForUser(Long currentUserId, int page, int size) {
        log.info("获取健康资讯列表，用户: {}, 页码: {}, 大小: {}", currentUserId, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));
        Page<Content> news = contentRepository.findByContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(
                Content.ContentType.NEWS, pageable);

        return news.map(content -> convertToContentListDTO(content, currentUserId));
    }

    /**
     * 获取医生指导列表（用户端）
     */
    @Transactional(readOnly = true)
    public Page<ContentListDTO> getDoctorGuidanceForUser(Long currentUserId, int page, int size) {
        log.info("获取医生指导列表，用户: {}, 页码: {}, 大小: {}", currentUserId, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));
        Page<Content> guidance = contentRepository.findByContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(
                Content.ContentType.GUIDANCE, pageable);

        return guidance.map(content -> convertToContentListDTO(content, currentUserId));
    }

    /**
     * 获取医生发布的健康指导列表
     */
    @Transactional(readOnly = true)
    public Page<ContentListDTO> getDoctorGuidanceByAuthor(Long doctorId, int page, int size) {
        log.info("获取医生健康指导列表，医生ID: {}, 页码: {}, 大小: {}", doctorId, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "publishedAt"));
        Page<Content> guidance = contentRepository.findByAuthorIdAndContentTypeAndPublishedAtIsNotNullOrderByPublishedAtDesc(
                doctorId, Content.ContentType.GUIDANCE, pageable);

        return guidance.map(content -> convertToContentListDTO(content, doctorId));
    }
}
