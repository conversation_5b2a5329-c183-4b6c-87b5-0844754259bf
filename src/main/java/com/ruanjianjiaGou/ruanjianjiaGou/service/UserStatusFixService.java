package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户状态修复服务
 * 用于修复数据库中用户状态为null的问题
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Order(100) // 确保在其他初始化服务之后执行
public class UserStatusFixService implements CommandLineRunner {

    private final UserRepository userRepository;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        log.info("开始修复用户状态数据...");
        
        try {
            // 查找所有status为null的用户
            List<User> usersWithNullStatus = userRepository.findAll().stream()
                    .filter(user -> user.getStatus() == null)
                    .toList();
            
            if (usersWithNullStatus.isEmpty()) {
                log.info("所有用户状态正常，无需修复");
                return;
            }
            
            log.info("发现 {} 个用户状态为null，开始修复...", usersWithNullStatus.size());
            
            // 为所有status为null的用户设置默认状态为ACTIVE
            for (User user : usersWithNullStatus) {
                user.setStatus(User.UserStatus.ACTIVE);
                userRepository.save(user);
                log.debug("用户 {} ({}) 状态已修复为ACTIVE", user.getNickname(), user.getPhoneNumber());
            }
            
            log.info("用户状态修复完成，共修复 {} 个用户", usersWithNullStatus.size());
            
            // 验证修复结果
            long nullStatusCount = userRepository.findAll().stream()
                    .filter(user -> user.getStatus() == null)
                    .count();
            
            if (nullStatusCount == 0) {
                log.info("用户状态修复验证通过，所有用户状态正常");
            } else {
                log.warn("用户状态修复后仍有 {} 个用户状态为null", nullStatusCount);
            }
            
        } catch (Exception e) {
            log.error("用户状态修复失败", e);
            throw e;
        }
    }
}
