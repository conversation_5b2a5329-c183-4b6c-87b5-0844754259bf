# 测试内容管理功能
Write-Host "=== 内容管理功能测试 ===" -ForegroundColor Green

# 1. 管理员登录
Write-Host "1. 管理员登录..." -ForegroundColor Yellow
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $adminToken = $loginResponse.data.token
    Write-Host "管理员登录成功" -ForegroundColor Green
} else {
    Write-Host "管理员登录失败" -ForegroundColor Red
    exit
}

$adminHeaders = @{
    "Authorization" = "Bearer $adminToken"
    "Content-Type" = "application/json"
}

# 2. 测试获取内容列表
Write-Host "2. 测试获取内容列表..." -ForegroundColor Yellow
$allContentsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Get -Headers $adminHeaders
Write-Host "所有内容数量: $($allContentsResponse.data.totalElements)" -ForegroundColor Green

# 3. 创建社区活动
Write-Host "3. 创建社区活动..." -ForegroundColor Yellow
$activityData = '{"contentType":"ACTIVITY","title":"春季健康体检活动","body":"为了提高社区居民的健康意识，我们将举办春季健康体检活动。","activityTime":"2025-07-01T09:00:00","activityLocation":"社区健康服务中心"}'
$createActivityResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Post -Headers $adminHeaders -Body $activityData
$activityId = $createActivityResponse.data.id
Write-Host "创建活动成功，ID: $activityId" -ForegroundColor Green

# 4. 用户登录
Write-Host "4. 用户登录..." -ForegroundColor Yellow
$userLoginBody = '{"phoneNumber":"13800000001","password":"123456"}'
$userLoginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $userLoginBody
$userToken = $userLoginResponse.data.token
Write-Host "用户登录成功" -ForegroundColor Green

$userHeaders = @{
    "Authorization" = "Bearer $userToken"
    "Content-Type" = "application/json"
}

# 5. 用户获取活动列表
Write-Host "5. 用户获取活动列表..." -ForegroundColor Yellow
$userActivitiesResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/activities" -Method Get -Headers $userHeaders
Write-Host "用户端活动数量: $($userActivitiesResponse.data.totalElements)" -ForegroundColor Green

# 6. 活动报名
Write-Host "6. 活动报名..." -ForegroundColor Yellow
$registerResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/contents/activities/$activityId/register" -Method Post -Headers $userHeaders
Write-Host "活动报名成功" -ForegroundColor Green

# 7. 删除测试活动
Write-Host "7. 删除测试活动..." -ForegroundColor Yellow
$deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents/$activityId" -Method Delete -Headers $adminHeaders
Write-Host "删除活动成功" -ForegroundColor Green

Write-Host "=== 测试完成 ===" -ForegroundColor Green
