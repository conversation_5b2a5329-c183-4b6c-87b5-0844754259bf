# Test Diagnosis Status Fix

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "=== 诊断状态修复验证测试 ===" -ForegroundColor Green

# 医生登录
Write-Host "`n1. 医生登录" -ForegroundColor Yellow

$doctorLoginData = @{
    phoneNumber = "13900000002"
    password = "123456"
} | ConvertTo-J<PERSON>

try {
    $doctorLoginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginData -Headers $headers
    Write-Host "医生登录成功" -ForegroundColor Green
    
    $doctorToken = $doctorLoginResponse.data.token
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $doctorToken"
    
    # 获取医生预约列表
    Write-Host "`n2. 获取医生预约列表" -ForegroundColor Yellow
    
    $appointmentsUrl = "$baseUrl/api/doctor/appointments/my?page=1&size=10"
    $appointmentsResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
    
    Write-Host "预约列表获取成功" -ForegroundColor Green
    Write-Host "总预约数: $($appointmentsResponse.data.totalElements)" -ForegroundColor Cyan
    
    if ($appointmentsResponse.data.content -and $appointmentsResponse.data.content.Count -gt 0) {
        Write-Host "`n预约详情:" -ForegroundColor White
        
        foreach ($appointment in $appointmentsResponse.data.content) {
            Write-Host "  预约ID: $($appointment.id)" -ForegroundColor Gray
            Write-Host "  患者姓名: $($appointment.profileOwnerName)" -ForegroundColor Yellow
            Write-Host "  预约状态: $($appointment.status)" -ForegroundColor Gray
            Write-Host "  诊断状态: $($appointment.diagnosisStatus)" -ForegroundColor Magenta
            Write-Host "  已诊断: $($appointment.hasDiagnosis)" -ForegroundColor Magenta
            Write-Host "  ---" -ForegroundColor Gray
        }
        
        # 查找一个未诊断的预约进行测试
        $undiagnosedAppointment = $appointmentsResponse.data.content | Where-Object { $_.hasDiagnosis -eq $false } | Select-Object -First 1
        
        if ($undiagnosedAppointment) {
            Write-Host "`n3. 测试诊断功能" -ForegroundColor Yellow
            Write-Host "选择预约ID: $($undiagnosedAppointment.id) 进行诊断测试" -ForegroundColor Cyan
            
            # 创建诊断处方
            $prescriptionData = @{
                profileId = $undiagnosedAppointment.profileId
                appointmentId = $undiagnosedAppointment.id
                diagnosis = "测试诊断 - 验证诊断状态修复功能"
                medications = @(
                    @{
                        name = "测试药品"
                        specification = "100mg"
                        quantity = 10
                        frequency = "每日2次"
                        dosage = "每次1片，饭后服用"
                        notes = "测试用药"
                    }
                )
            } | ConvertTo-Json -Depth 3
            
            try {
                $prescriptionUrl = "$baseUrl/api/doctor/appointments/$($undiagnosedAppointment.id)/prescribe"
                $prescriptionResponse = Invoke-RestMethod -Uri $prescriptionUrl -Method POST -Body $prescriptionData -Headers $authHeaders
                
                Write-Host "✅ 诊断处方创建成功!" -ForegroundColor Green
                Write-Host "处方ID: $($prescriptionResponse.data.prescription.id)" -ForegroundColor Cyan
                
                # 重新获取预约列表验证状态更新
                Write-Host "`n4. 验证诊断状态更新" -ForegroundColor Yellow
                
                Start-Sleep -Seconds 2
                $updatedResponse = Invoke-RestMethod -Uri $appointmentsUrl -Method GET -Headers $authHeaders
                $updatedAppointment = $updatedResponse.data.content | Where-Object { $_.id -eq $undiagnosedAppointment.id }
                
                if ($updatedAppointment) {
                    Write-Host "更新后状态:" -ForegroundColor Green
                    Write-Host "  预约状态: $($updatedAppointment.status)" -ForegroundColor Gray
                    Write-Host "  诊断状态: $($updatedAppointment.diagnosisStatus)" -ForegroundColor Magenta
                    Write-Host "  已诊断: $($updatedAppointment.hasDiagnosis)" -ForegroundColor Magenta
                    
                    if ($updatedAppointment.hasDiagnosis -eq $true) {
                        Write-Host "`n🎉 SUCCESS: 诊断状态修复成功!" -ForegroundColor Green
                        Write-Host "   ✅ 诊断处方创建成功" -ForegroundColor White
                        Write-Host "   ✅ 预约状态正确更新" -ForegroundColor White
                        Write-Host "   ✅ 诊断状态正确显示" -ForegroundColor White
                    } else {
                        Write-Host "`n❌ ISSUE: 诊断状态未正确更新" -ForegroundColor Red
                    }
                }
                
            } catch {
                Write-Host "❌ 创建诊断处方失败: $($_.Exception.Message)" -ForegroundColor Red
                if ($_.ErrorDetails.Message) {
                    Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
                }
            }
            
        } else {
            Write-Host "`n所有预约都已诊断，无法测试诊断功能" -ForegroundColor Yellow
            
            # 显示所有预约的诊断状态
            Write-Host "`n当前所有预约的诊断状态:" -ForegroundColor White
            foreach ($appointment in $appointmentsResponse.data.content) {
                $statusColor = if ($appointment.hasDiagnosis) { "Green" } else { "Red" }
                Write-Host "  预约$($appointment.id): $($appointment.diagnosisStatus)" -ForegroundColor $statusColor
            }
        }
        
    } else {
        Write-Host "没有找到预约记录" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "测试失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

Write-Host "`n前端验证:" -ForegroundColor Cyan
Write-Host "- 前端地址: http://localhost:5173" -ForegroundColor White
Write-Host "- 医生预约管理: http://localhost:5173/doctor/appointments" -ForegroundColor White
Write-Host "- 测试账号: 13900000002 / 123456" -ForegroundColor White

Write-Host "`n修复说明:" -ForegroundColor Yellow
Write-Host "1. 修复了诊断状态判断逻辑" -ForegroundColor White
Write-Host "2. 现在基于当前预约是否有处方记录判断" -ForegroundColor White
Write-Host "3. 而不是基于患者的所有处方记录" -ForegroundColor White
Write-Host "4. 前端显示的诊断状态现在应该正确" -ForegroundColor White
