# 测试医生审核功能
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $token"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 测试获取医生列表
    Write-Host "`n=== 测试获取医生列表 ==="
    try {
        $doctorsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors?page=1&size=10" -Method Get -Headers $headers
        Write-Host "医生列表响应: $($doctorsResponse | ConvertTo-Json -Depth 3)"
        
        if ($doctorsResponse.code -eq 200 -and $doctorsResponse.data.content.Count -gt 0) {
            $firstDoctor = $doctorsResponse.data.content[0]
            $doctorUserId = $firstDoctor.userId
            Write-Host "选择医生进行测试: $doctorUserId"
            
            # 测试获取医生审核详情
            Write-Host "`n=== 测试获取医生审核详情 ==="
            try {
                $reviewDetailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors/$doctorUserId/review" -Method Get -Headers $headers
                Write-Host "医生审核详情响应: $($reviewDetailResponse | ConvertTo-Json -Depth 3)"
            } catch {
                Write-Host "获取医生审核详情失败: $($_.Exception.Message)"
            }
        }
    } catch {
        Write-Host "获取医生列表失败: $($_.Exception.Message)"
    }
    
    # 测试获取待审核医生列表
    Write-Host "`n=== 测试获取待审核医生列表 ==="
    try {
        $pendingDoctorsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors?status=PENDING&page=1&size=10" -Method Get -Headers $headers
        Write-Host "待审核医生列表响应: $($pendingDoctorsResponse | ConvertTo-Json -Depth 3)"
        
        if ($pendingDoctorsResponse.code -eq 200 -and $pendingDoctorsResponse.data.content.Count -gt 0) {
            $pendingDoctor = $pendingDoctorsResponse.data.content[0]
            $pendingDoctorUserId = $pendingDoctor.userId
            Write-Host "选择待审核医生进行测试: $pendingDoctorUserId"
            
            # 测试审核通过
            Write-Host "`n=== 测试审核通过 ==="
            try {
                $approveBody = '{"status":"APPROVED","reason":"资质审核通过，符合要求"}'
                $approveResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/doctors/$pendingDoctorUserId/review" -Method Put -Headers $headers -Body $approveBody
                Write-Host "审核通过响应: $($approveResponse | ConvertTo-Json)"
            } catch {
                Write-Host "审核通过失败: $($_.Exception.Message)"
            }
        }
    } catch {
        Write-Host "获取待审核医生列表失败: $($_.Exception.Message)"
    }
} else {
    Write-Host "登录失败: $($loginResponse.message)"
}
