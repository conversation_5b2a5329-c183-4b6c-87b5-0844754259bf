-- 为电子处方表添加预约ID字段
-- 这个脚本用于修复诊断状态显示问题

USE community_health_db;

-- 添加appointment_id字段到e_prescriptions表
ALTER TABLE e_prescriptions 
ADD COLUMN appointment_id INT UNSIGNED NULL COMMENT '关联的预约ID' 
AFTER consultation_id;

-- 添加外键索引
ALTER TABLE e_prescriptions 
ADD INDEX fk_prescriptions_appointments_idx (appointment_id ASC);

-- 添加外键约束（可选，如果需要严格的数据完整性）
-- ALTER TABLE e_prescriptions 
-- ADD CONSTRAINT fk_prescriptions_appointments 
-- FOREIGN KEY (appointment_id) REFERENCES appointments (id) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 查看表结构确认修改
DESCRIBE e_prescriptions;
