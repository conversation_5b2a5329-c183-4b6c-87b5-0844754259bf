@echo off
echo === Content Management Test ===

echo 1. Admin Login...
curl -X POST "http://localhost:8080/api/user/login" -H "Content-Type: application/json" -d "{\"phoneNumber\":\"19999999999\",\"password\":\"admin888\"}" > admin_login.json

echo 2. Get All Contents...
for /f "tokens=2 delims=:" %%a in ('findstr "token" admin_login.json') do set TOKEN=%%a
set TOKEN=%TOKEN:"=%
set TOKEN=%TOKEN:}=%
set TOKEN=%TOKEN: =%
curl -X GET "http://localhost:8080/api/admin/contents" -H "Authorization: Bearer %TOKEN%"

echo 3. User Login...
curl -X POST "http://localhost:8080/api/user/login" -H "Content-Type: application/json" -d "{\"phoneNumber\":\"13800000001\",\"password\":\"123456\"}" > user_login.json

echo 4. Get User Activities...
for /f "tokens=2 delims=:" %%a in ('findstr "token" user_login.json') do set USER_TOKEN=%%a
set USER_TOKEN=%USER_TOKEN:"=%
set USER_TOKEN=%USER_TOKEN:}=%
set USER_TOKEN=%USER_TOKEN: =%
curl -X GET "http://localhost:8080/api/user/contents/activities" -H "Authorization: Bearer %USER_TOKEN%"

echo === Test Complete ===
del admin_login.json user_login.json
