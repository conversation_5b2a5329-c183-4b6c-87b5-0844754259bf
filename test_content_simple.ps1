# 测试内容管理功能
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "登录成功"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 测试获取所有内容列表
    Write-Host "测试获取所有内容列表..."
    try {
        $allContentsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Get -Headers $headers
        Write-Host "所有内容数量: $($allContentsResponse.data.totalElements)"
    } catch {
        Write-Host "获取所有内容失败: $($_.Exception.Message)"
    }
    
    # 测试创建新内容
    Write-Host "测试创建新内容..."
    try {
        $createBody = '{"contentType":"NEWS","title":"测试健康资讯","body":"这是测试内容"}'
        $createResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents" -Method Post -Headers $headers -Body $createBody
        Write-Host "创建内容成功: $($createResponse.data.title)"
        
        $newContentId = $createResponse.data.id
        Write-Host "新内容ID: $newContentId"
        
        # 测试删除内容
        Write-Host "测试删除内容..."
        $deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/contents/$newContentId" -Method Delete -Headers $headers
        Write-Host "删除内容成功"
        
    } catch {
        Write-Host "内容操作失败: $($_.Exception.Message)"
    }
    
} else {
    Write-Host "登录失败"
}
