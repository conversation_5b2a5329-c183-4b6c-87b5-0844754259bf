# 管理员登录并测试用户管理API
$loginBody = Get-Content test_login.json -Raw
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

Write-Host "登录响应: $($loginResponse | ConvertTo-Json)"

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "Token: $token"
    
    # 测试获取用户列表
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    Write-Host "测试获取用户列表..."
    try {
        $usersResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/users?page=1&size=10" -Method Get -Headers $headers
        Write-Host "用户列表响应: $($usersResponse | ConvertTo-Json -Depth 3)"
    } catch {
        Write-Host "获取用户列表失败: $($_.Exception.Message)"
    }
    
    # 测试获取用户详情 (假设用户ID为1)
    Write-Host "测试获取用户详情..."
    try {
        $userDetailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/users/1" -Method Get -Headers $headers
        Write-Host "用户详情响应: $($userDetailResponse | ConvertTo-Json -Depth 3)"
    } catch {
        Write-Host "获取用户详情失败: $($_.Exception.Message)"
    }
} else {
    Write-Host "登录失败: $($loginResponse.message)"
}
