# 测试用户状态更新功能
$loginBody = '{"phoneNumber":"19999999999","password":"admin888"}'
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody

if ($loginResponse.code -eq 200) {
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $token"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # 测试禁用用户 (用户ID 1)
    Write-Host "测试禁用用户ID 1..."
    try {
        $disableBody = '{"status":"DISABLED"}'
        $disableResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/users/1/status" -Method Put -Headers $headers -Body $disableBody
        Write-Host "禁用用户响应: $($disableResponse | ConvertTo-Json)"
    } catch {
        Write-Host "禁用用户失败: $($_.Exception.Message)"
    }
    
    # 再次获取用户详情确认状态变更
    Write-Host "确认用户状态变更..."
    try {
        $userDetailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/users/1" -Method Get -Headers $headers
        Write-Host "用户状态: $($userDetailResponse.data.statusDescription)"
    } catch {
        Write-Host "获取用户详情失败: $($_.Exception.Message)"
    }
    
    # 测试重新启用用户
    Write-Host "测试重新启用用户ID 1..."
    try {
        $enableBody = '{"status":"ACTIVE"}'
        $enableResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/admin/users/1/status" -Method Put -Headers $headers -Body $enableBody
        Write-Host "启用用户响应: $($enableResponse | ConvertTo-Json)"
    } catch {
        Write-Host "启用用户失败: $($_.Exception.Message)"
    }
} else {
    Write-Host "登录失败: $($loginResponse.message)"
}
